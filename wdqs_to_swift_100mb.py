# wdqs_to_swift_100mb.py  (deadline + verbose + qlever-friendly)
import requests, time, sys, argparse, random, urllib.parse

try:
    from pypinyin import lazy_pinyin, Style
except Exception:
    lazy_pinyin = None
    Style = None

SPARQL_BODY = r"""
PREFIX wd:  <http://www.wikidata.org/entity/>
PREFIX wdt: <http://www.wikidata.org/prop/direct/>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>

SELECT DISTINCT ?item ?itemLabel
       (COALESCE(?zh, ?zhHans, ?zhHant, ?zhCN, ?zhTW, ?zhHK, ?itemLabel) AS ?meaning_zh)
       ?categoryLabel
WHERE {
  VALUES (?root ?categoryLabel) {
    (wd:Q739589 "装饰音")
    (wd:Q4334   "装饰音")
    (wd:Q372923 "演奏技巧")
    (wd:Q113558 "力度")
    (wd:Q174047 "速度")
    (wd:Q233861 "记谱符号")
    (wd:Q189962 "音程")
    (wd:Q179651 "音阶/调式")
    (wd:Q170439 "和弦/和声")
  }
  { ?item wdt:P31/wdt:P279* ?root . } UNION { ?item wdt:P279* ?root . }

  OPTIONAL { ?item rdfs:label ?itemLabel FILTER (LANG(?itemLabel) = "en") }
  OPTIONAL { ?item rdfs:label ?zh      FILTER (LANG(?zh)      = "zh") }
  OPTIONAL { ?item rdfs:label ?zhHans  FILTER (LANG(?zhHans)  = "zh-hans") }
  OPTIONAL { ?item rdfs:label ?zhHant  FILTER (LANG(?zhHant)  = "zh-hant") }
  OPTIONAL { ?item rdfs:label ?zhCN    FILTER (LANG(?zhCN)    = "zh-cn") }
  OPTIONAL { ?item rdfs:label ?zhTW    FILTER (LANG(?zhTW)    = "zh-tw") }
  OPTIONAL { ?item rdfs:label ?zhHK    FILTER (LANG(?zhHK)    = "zh-hk") }
}
"""

def to_pinyin(s: str) -> str:
    if not s or not lazy_pinyin: return ""
    return "".join(lazy_pinyin(s, style=Style.NORMAL if Style else None))

def _try(sess, ep, q, headers, connect_to, read_to, prefer_get=False):
    if prefer_get:
        try:
            return sess.get(ep, params={"query": q}, headers={"Accept": headers["Accept"],
                                                              "User-Agent": headers["User-Agent"]},
                            timeout=(connect_to, read_to))
        except requests.exceptions.RequestException:
            pass
    return sess.post(ep, data=q.encode("utf-8"), headers=headers, timeout=(connect_to, read_to))

def fetch_page(limit, offset, session, ua, endpoints, connect_to, read_to, retries, base_sleep):
    q = f"{SPARQL_BODY}\nLIMIT {int(limit)}\nOFFSET {int(offset)}\n"  # 无 ORDER BY，QLever 更快
    headers = {"Accept": "application/sparql-results+json",
               "Content-Type": "application/sparql-query; charset=utf-8",
               "User-Agent": ua}
    last_err = None
    for attempt in range(retries):
        for ep in endpoints:
            host = urllib.parse.urlparse(ep).hostname or ""
            prefer_get = "qlever.cs.uni-freiburg.de" in host  # QLever 用 GET 更稳
            try:
                r = _try(session, ep, q, headers, connect_to, read_to, prefer_get)
                if r.status_code == 429:
                    ra = r.headers.get("Retry-After")
                    wait = float(ra) if (ra and ra.isdigit()) else base_sleep * (2 ** attempt)
                    time.sleep(min(wait + random.random() * 0.5, 25))
                    continue
                r.raise_for_status()
                return r.json()["results"]["bindings"]
            except requests.exceptions.RequestException as e:
                last_err = e
                continue
        time.sleep(min(base_sleep * (2 ** attempt) + random.random(), 15))
    if last_err: raise last_err
    return []

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--out", default="InitialTermDataSeed.swift")
    ap.add_argument("--max-mb", type=float, default=100.0)
    ap.add_argument("--page-size", type=int, default=200)
    ap.add_argument("--start-offset", type=int, default=0)
    ap.add_argument("--endpoint", action="append")
    ap.add_argument("--proxy", default="")
    ap.add_argument("--connect-timeout", type=float, default=15.0)
    ap.add_argument("--read-timeout", type=float, default=25.0)
    ap.add_argument("--retries", type=int, default=2)
    ap.add_argument("--sleep", type=float, default=0.3)
    ap.add_argument("--deadline", type=int, default=90, help="总时长上限（秒）")
    ap.add_argument("--verbose", action="store_true")
    args = ap.parse_args()

    endpoints = args.endpoint or [
        "https://qlever.cs.uni-freiburg.de/api/wikidata",
        "https://query.wikidata.org/sparql",
    ]

    sess = requests.Session()
    if args.proxy:
        sess.trust_env = False
        sess.proxies.update({"http": args.proxy, "https": args.proxy})
    ua = "wdqs-to-swift/1.4 (+contact: <EMAIL>)"

    max_bytes = int(args.max_mb * 1024 * 1024)
    bytes_written = 0
    seen = set()
    total_rows = 0
    deadline_ts = time.time() + args.deadline

    with open(args.out, "w", encoding="utf-8") as f:
        offset = args.start_offset
        while True:
            if time.time() > deadline_ts:
                print(f"[STOP] hit deadline {args.deadline}s, wrote {total_rows} rows, {bytes_written} bytes.",
                      file=sys.stderr)
                break

            try:
                bindings = fetch_page(args.page_size, offset, sess, ua, endpoints,
                                      args.connect_timeout, args.read_timeout, args.retries, args.sleep)
            except Exception as e:
                print(f"[WARN] fetch fail offset={offset}: {e}", file=sys.stderr)
                time.sleep(2)
                continue

            if not bindings:
                if args.verbose: print(f"[DONE] no more bindings at offset={offset}", file=sys.stderr)
                break

            for b in bindings:
                term = b.get("itemLabel", {}).get("value")
                if not term:
                    term = b["item"]["value"].rsplit("/", 1)[-1]
                meaning = b.get("meaning_zh", {}).get("value", term)
                category = b["categoryLabel"]["value"]
                key = (term, meaning, category)
                if key in seen:
                    continue
                seen.add(key)
                py = "".join(lazy_pinyin(meaning, style=Style.NORMAL)) if lazy_pinyin else ""
                line = f'InitialTermData(term: "{term}", meaning: "{meaning}", pinyin: "{py}", category: "{category}"),\n'
                enc = line.encode("utf-8")
                if bytes_written + len(enc) > max_bytes:
                    print(f"[STOP] reached {bytes_written/1024/1024:.2f}MB (limit {args.max_mb}MB).", file=sys.stderr)
                    print(f"Wrote {total_rows} rows, {bytes_written} bytes.")
                    return
                f.write(line)
                bytes_written += len(enc)
                total_rows += 1

            if args.verbose:
                print(f"[PAGE] offset={offset} ++{len(bindings)} rows, total={total_rows}, size={bytes_written/1024/1024:.2f}MB",
                      file=sys.stderr)
            offset += args.page_size
            time.sleep(args.sleep)

    print(f"Done. Wrote {total_rows} rows, {bytes_written} bytes (<= {args.max_mb} MB) to {args.out}")

if __name__ == "__main__":
    main()