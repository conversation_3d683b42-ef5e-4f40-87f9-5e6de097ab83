# iOS音乐术语词典

一个现代化的iOS音乐术语查询应用，基于Python版本重新设计开发。

## 功能特点

### 核心功能
- **智能搜索**：支持中文、拼音、英文多种搜索方式
- **模糊匹配**：智能匹配相关术语，提供最佳搜索体验
- **A-Z分类**：按字母顺序浏览所有音乐术语
- **收藏管理**：收藏重要术语，便于快速查阅
- **历史记录**：记录搜索历史，支持练习模式
- **导出功能**：支持术语导出和分享

### 界面设计
- **极简黑白**：纯粹的黑白配色，拒绝一切彩色元素
- **线条美学**：通过边框和分割线创造结构感
- **等宽字体**：使用SF Mono增强技术感
- **网格布局**：严格的网格系统保证界面秩序感

### 技术架构
- **SwiftUI**：现代化的用户界面框架
- **SwiftData**：本地数据存储和管理
- **Combine**：响应式编程和数据流管理
- **iOS 17+**：支持最新iOS特性

## 项目结构

```
MusicTermsDictionary/
├── App/
│   ├── MusicTermsDictionaryApp.swift
│   └── ContentView.swift
├── Models/
│   ├── MusicTerm.swift
│   ├── Favorite.swift
│   └── SearchHistory.swift
├── Views/
│   ├── Home/
│   │   ├── HomeView.swift
│   │   ├── SearchBar.swift
│   │   └── TermListView.swift
│   ├── Favorites/
│   │   └── FavoritesView.swift
│   ├── History/
│   │   └── HistoryView.swift
│   └── Components/
│       ├── TermCard.swift
│       └── NavigationBar.swift
├── Services/
│   ├── SearchService.swift
│   ├── DataManager.swift
│   └── ExportService.swift
└── Resources/
    ├── MusicTermsData.json
    └── Assets.xcassets
```

## 开发进度

- [x] 项目规划和架构设计
- [x] 数据模型设计
- [x] 核心搜索功能实现
- [x] 主界面UI设计
- [x] 底部导航栏实现
- [x] 收藏功能实现
- [x] 历史记录功能
- [x] 导出功能实现
- [x] 项目优化和测试

## 功能详细说明

### 智能搜索系统
- **多语言支持**：支持中文、拼音、英文三种搜索方式
- **智能匹配**：精确匹配、前缀匹配、包含匹配、拼音匹配、释义匹配、模糊匹配
- **搜索建议**：实时搜索建议和自动补全
- **匹配度显示**：显示搜索结果的匹配度百分比

### 术语管理
- **分类浏览**：按A-Z字母顺序分组显示
- **详细信息**：术语名称、释义、拼音、分类等完整信息
- **快速定位**：点击字母快速跳转到对应分组

### 收藏系统
- **一键收藏**：在术语详情页面快速收藏/取消收藏
- **笔记功能**：为收藏的术语添加个人笔记
- **收藏搜索**：在收藏夹中搜索特定术语
- **批量管理**：支持清空收藏夹等批量操作

### 历史记录
- **搜索历史**：记录所有搜索活动，按时间分组显示
- **热门搜索**：统计最常搜索的术语
- **重新搜索**：一键重新执行历史搜索
- **练习模式**：基于历史数据的术语练习功能

### 练习模式
- **自定义练习**：选择题目数量和难度等级
- **进度跟踪**：实时显示练习进度
- **成绩统计**：练习完成后显示详细成绩报告
- **重复练习**：支持重新开始练习

### 导出功能
- **多格式支持**：支持文本、JSON、CSV三种导出格式
- **灵活选择**：可导出全部术语、收藏术语、搜索历史等
- **系统分享**：通过iOS系统分享功能分享导出文件

## 安装和运行

### 系统要求
- Xcode 15.0+
- iOS 17.0+
- macOS 14.0+

### 运行步骤
1. 克隆项目到本地
2. 使用Xcode打开 `MusicTermsDictionary.xcodeproj`
3. 选择目标设备或模拟器
4. 点击运行按钮或按 `Cmd+R`

## 数据来源

音乐术语数据来源于原Python版本，包含500+专业音乐术语，涵盖：
- 演奏技巧术语
- 音乐表情术语
- 乐器相关术语
- 音乐理论术语
- 作曲技法术语

## 设计理念

### 视觉哲学
1. **纯粹黑白**：拒绝一切彩色元素，坚持黑白两色的纯净表达
2. **线条美学**：强调线条的力量，通过边框和分割线创造结构感
3. **留白艺术**：适当的留白创造呼吸感，避免视觉拥挤
4. **等宽字体**：使用SF Mono等等宽字体，增强技术感和整齐度
5. **网格布局**：严格的网格系统，保证界面的秩序感和平衡感

### 设计态度
- 彩色是世界上最丑陋的语言
- 美感源于克制而非装饰
- 功能性与美学并重
- 细节之处见真章
- 简约不等于简单

## 开发者

由yuze研发，基于原Python版本重新设计的iOS应用。

## 许可证

MIT License - 完全开源
