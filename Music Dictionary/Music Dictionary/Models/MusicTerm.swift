//
//  MusicTerm.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import Foundation
import SwiftData

@Model
final class MusicTerm {
    var id: UUID
    var term: String
    var meaning: String
    var pinyin: String
    var category: String
    var createdAt: Date
    
    init(term: String, meaning: String, pinyin: String = "", category: String = "") {
        self.id = UUID()
        self.term = term
        self.meaning = meaning
        self.pinyin = pinyin
        self.category = category
        self.createdAt = Date()
    }
    
    // 计算属性：获取术语的首字母
    var firstLetter: String {
        if let firstChar = term.first {
            if firstChar.isASCII && firstChar.isLetter {
                return String(firstChar).uppercased()
            } else {
                // 对于中文或其他字符，返回拼音首字母
                return String(pinyin.prefix(1)).uppercased()
            }
        }
        return "#"
    }
    
    // 计算属性：判断是否为英文术语
    var isEnglishTerm: Bool {
        return term.allSatisfy { $0.isASCII }
    }
    
    // 计算属性：判断是否为中文术语
    var isChineseTerm: Bool {
        return term.contains { $0.unicodeScalars.contains { $0.value >= 0x4E00 && $0.value <= 0x9FFF } }
    }
}

// 扩展：搜索相关方法
extension MusicTerm {
    // 检查术语是否匹配搜索关键词
    func matches(searchText: String) -> Bool {
        let lowercaseSearch = searchText.lowercased()
        
        // 检查术语本身
        if term.lowercased().contains(lowercaseSearch) {
            return true
        }
        
        // 检查释义
        if meaning.lowercased().contains(lowercaseSearch) {
            return true
        }
        
        // 检查拼音
        if pinyin.lowercased().contains(lowercaseSearch) {
            return true
        }
        
        return false
    }
    
    // 计算与搜索词的匹配度
    func matchScore(for searchText: String) -> Double {
        let lowercaseSearch = searchText.lowercased()
        let lowercaseTerm = term.lowercased()
        let lowercaseMeaning = meaning.lowercased()
        let lowercasePinyin = pinyin.lowercased()
        
        var score: Double = 0
        
        // 精确匹配最高分
        if lowercaseTerm == lowercaseSearch {
            score += 1.0
        } else if lowercaseTerm.hasPrefix(lowercaseSearch) {
            score += 0.8
        } else if lowercaseTerm.contains(lowercaseSearch) {
            score += 0.6
        }
        
        // 释义匹配
        if lowercaseMeaning.contains(lowercaseSearch) {
            score += 0.4
        }
        
        // 拼音匹配
        if lowercasePinyin == lowercaseSearch {
            score += 0.9
        } else if lowercasePinyin.hasPrefix(lowercaseSearch) {
            score += 0.7
        } else if lowercasePinyin.contains(lowercaseSearch) {
            score += 0.5
        }
        
        return min(score, 1.0)
    }
}
