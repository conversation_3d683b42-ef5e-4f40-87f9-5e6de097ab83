//
//  ContentView.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house")
                    Text("主页")
                }
                .tag(0)

            FavoritesView()
                .tabItem {
                    Image(systemName: "heart")
                    Text("收藏夹")
                }
                .tag(1)

            PracticeView()
                .tabItem {
                    Image(systemName: "brain.head.profile")
                    Text("练习模式")
                }
                .tag(2)
        }
        .accentColor(.black)
        .preferredColorScheme(.light)
    }
}

#Preview {
    ContentView()
}
