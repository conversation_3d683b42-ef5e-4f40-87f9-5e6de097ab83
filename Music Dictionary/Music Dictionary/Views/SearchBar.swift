//
//  SearchBar.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import SwiftUI

struct SearchBar: View {
    @Binding var text: String
    @Binding var isSearching: Bool
    let onSearchChanged: () -> Void
    let onClear: () -> Void
    
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // 搜索图标
            Image(systemName: "magnifyingglass")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.black.opacity(0.6))
            
            // 搜索输入框
            TextField("搜索音乐术语...", text: $text)
                .font(.system(.body, design: .monospaced))
                .foregroundColor(.black)
                .focused($isTextFieldFocused)
                .onChange(of: text) { _, newValue in
                    onSearchChanged()
                }
                .onTapGesture {
                    isSearching = true
                }
            
            // 清除按钮
            if !text.isEmpty {
                Button(action: {
                    text = ""
                    onClear()
                    isTextFieldFocused = false
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.black.opacity(0.4))
                }
                .transition(.scale.combined(with: .opacity))
            }
            
            // 取消按钮
            if isSearching {
                Button("取消") {
                    text = ""
                    onClear()
                    isTextFieldFocused = false
                }
                .font(.system(.body, design: .monospaced))
                .foregroundColor(.black)
                .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 0)
                .stroke(Color.black, lineWidth: 2)
                .background(Color.white)
        )
        .animation(.easeInOut(duration: 0.2), value: isSearching)
        .animation(.easeInOut(duration: 0.2), value: text.isEmpty)
        .onChange(of: isTextFieldFocused) { _, focused in
            if focused {
                isSearching = true
            } else if text.isEmpty {
                isSearching = false
            }
        }
    }
}

#Preview {
    VStack {
        SearchBar(
            text: .constant("dolce"),
            isSearching: .constant(true),
            onSearchChanged: {},
            onClear: {}
        )
        
        Spacer()
    }
    .padding()
    .background(Color.white)
}
