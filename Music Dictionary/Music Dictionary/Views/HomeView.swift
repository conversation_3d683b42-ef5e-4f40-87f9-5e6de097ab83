//
//  HomeView.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import SwiftUI
import SwiftData

struct HomeView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var searchText = ""
    @State private var searchResults: [SearchResult] = []
    @State private var isSearching = false
    @State private var showingAllTerms = true
    @State private var selectedTerm: MusicTerm?
    
    @State private var dataManager = DataManager()
    @State private var searchService = SearchService()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                SearchBar(
                    text: $searchText,
                    isSearching: $isSearching,
                    onSearchChanged: performSearch,
                    onClear: clearSearch
                )
                .padding(.horizontal, 16)
                .padding(.top, 8)
                
                // 内容区域
                if isSearching && !searchText.isEmpty {
                    // 搜索结果
                    SearchResultsView(
                        results: searchResults,
                        searchText: searchText,
                        onTermSelected: { term in
                            selectedTerm = term
                        }
                    )
                } else {
                    // 所有术语列表
                    AllTermsView(
                        terms: dataManager.musicTerms,
                        onTermSelected: { term in
                            selectedTerm = term
                        }
                    )
                }
                
                Spacer()
            }
            .navigationTitle("音乐术语词典")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.white)
            .onAppear {
                setupServices()
            }
            .sheet(item: $selectedTerm) { term in
                TermDetailView(term: term)
            }
        }
        .preferredColorScheme(.light)
    }
    
    // 设置服务
    private func setupServices() {
        dataManager.setModelContext(modelContext)
        searchService.setModelContext(modelContext)
    }
    
    // 执行搜索
    private func performSearch() {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            searchResults = []
            showingAllTerms = true
            return
        }
        
        showingAllTerms = false
        searchResults = searchService.search(searchText, in: dataManager.musicTerms)
    }
    
    // 清除搜索
    private func clearSearch() {
        searchText = ""
        searchResults = []
        showingAllTerms = true
        isSearching = false
    }
    

}

// 搜索结果视图
struct SearchResultsView: View {
    let results: [SearchResult]
    let searchText: String
    let onTermSelected: (MusicTerm) -> Void
    
    var body: some View {
        if results.isEmpty {
            // 无结果状态
            VStack(spacing: 16) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 48, weight: .thin))
                    .foregroundColor(.black.opacity(0.3))
                
                Text("未找到相关术语")
                    .font(.system(.title2, design: .monospaced))
                    .foregroundColor(.black)
                
                Text("尝试使用不同的关键词")
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("搜索建议：")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.8))
                    
                    Text("• 使用中文、拼音或英文")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                    
                    Text("• 尝试更短的关键词")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                    
                    Text("• 检查拼写是否正确")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                }
                .padding(.top, 8)
            }
            .padding(.top, 60)
            .frame(maxWidth: .infinity)
        } else {
            // 搜索结果列表
            ScrollView {
                LazyVStack(spacing: 1) {
                    ForEach(Array(results.enumerated()), id: \.element.term.id) { index, result in
                        SearchResultCard(
                            result: result,
                            searchText: searchText,
                            index: index + 1
                        ) {
                            onTermSelected(result.term)
                        }
                    }
                }
                .padding(.top, 16)
            }
        }
    }
}

// 所有术语视图
struct AllTermsView: View {
    let terms: [MusicTerm]
    let onTermSelected: (MusicTerm) -> Void
    
    private var groupedTerms: [(String, [MusicTerm])] {
        let grouped = Dictionary(grouping: terms) { term in
            term.firstLetter
        }
        return grouped.sorted { $0.key < $1.key }
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
                ForEach(groupedTerms, id: \.0) { letter, termsInGroup in
                    Section {
                        ForEach(termsInGroup, id: \.id) { term in
                            TermCard(term: term) {
                                onTermSelected(term)
                            }
                            
                            // 分割线
                            if term.id != termsInGroup.last?.id {
                                Rectangle()
                                    .fill(Color.black.opacity(0.1))
                                    .frame(height: 1)
                                    .padding(.leading, 16)
                            }
                        }
                    } header: {
                        SectionHeader(letter: letter)
                    }
                }
            }
            .padding(.top, 16)
        }
    }
}

// 分组标题
struct SectionHeader: View {
    let letter: String
    
    var body: some View {
        HStack {
            Text(letter)
                .font(.system(.title, design: .monospaced, weight: .bold))
                .foregroundColor(.black)
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.white)
        .overlay(
            Rectangle()
                .fill(Color.black)
                .frame(height: 2),
            alignment: .bottom
        )
    }
}

#Preview {
    HomeView()
        .modelContainer(for: [MusicTerm.self, Favorite.self])
}
