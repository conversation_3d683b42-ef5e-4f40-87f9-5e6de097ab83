//
//  PracticeView.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import SwiftUI
import SwiftData
import Foundation

struct PracticeView: View {
    @Environment(\.modelContext) private var modelContext
    
    @State private var practiceMode = false
    @State private var currentPracticeIndex = 0
    @State private var practiceTerms: [MusicTerm] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if practiceMode {
                    PracticeModeView(
                        terms: practiceTerms,
                        currentIndex: $currentPracticeIndex,
                        onExit: {
                            practiceMode = false
                            currentPracticeIndex = 0
                        }
                    )
                } else {
                    PracticeSetupView { terms in
                        practiceTerms = terms
                        practiceMode = true
                        currentPracticeIndex = 0
                    }
                }
            }
            .navigationTitle("练习模式")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.white)
        }
        .preferredColorScheme(.light)
    }
}

// 练习设置视图
struct PracticeSetupView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var selectedCount = 10
    
    private let countOptions = [5, 10, 20, 50]
    
    let onStartPractice: ([MusicTerm]) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 添加顶部间距，让整体往下移
            Spacer()
                .frame(height: 80)
            
            VStack(spacing: 32) {
                VStack(spacing: 16) {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 64, weight: .thin))
                        .foregroundColor(.black.opacity(0.6))
                    
                    Text("练习模式")
                        .font(.system(.title, design: .monospaced, weight: .bold))
                        .foregroundColor(.black)
                    
                    Text("通过练习加深对音乐术语的理解")
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.black.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
                
                // 题目数量选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("题目数量")
                        .font(.system(.headline, design: .monospaced, weight: .semibold))
                        .foregroundColor(.black)
                    
                    HStack(spacing: 8) {
                        ForEach(countOptions, id: \.self) { count in
                            Button("\(count)") {
                                selectedCount = count
                            }
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(selectedCount == count ? .white : .black)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .fill(selectedCount == count ? Color.black : Color.white)
                                    .stroke(Color.black, lineWidth: 1)
                            )
                        }
                    }
                }
                
                Button("开始练习") {
                    startPractice()
                }
                .font(.system(.headline, design: .monospaced, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 12)
                .background(Color.black)
                .disabled(selectedCount == 0)
                
                Spacer()
            }
            .padding(32)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
    
    private func startPractice() {
        // 获取术语数据
        let descriptor = FetchDescriptor<MusicTerm>()
        
        do {
            let allTerms = try modelContext.fetch(descriptor)
            let practiceTerms = Array(allTerms.shuffled().prefix(selectedCount))
            onStartPractice(practiceTerms)
        } catch {
            print("获取练习术语失败: \(error)")
        }
    }
}

// 练习模式视图
struct PracticeModeView: View {
    let terms: [MusicTerm]
    @Binding var currentIndex: Int
    let onExit: () -> Void
    
    @State private var showingAnswer = false
    @State private var userAnswer = ""
    @State private var score = 0
    @State private var showingResult = false
    
    private var currentTerm: MusicTerm? {
        guard currentIndex < terms.count else { return nil }
        return terms[currentIndex]
    }
    
    private var progress: Double {
        guard !terms.isEmpty else { return 0 }
        return Double(currentIndex) / Double(terms.count)
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 进度条
            VStack(spacing: 8) {
                HStack {
                    Text("进度")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                    
                    Spacer()
                    
                    Text("\(currentIndex + 1) / \(terms.count)")
                        .font(.system(.caption, design: .monospaced, weight: .semibold))
                        .foregroundColor(.black)
                }
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .black))
                    .background(Color.black.opacity(0.1))
            }
            
            if let term = currentTerm {
                // 题目内容
                VStack(spacing: 16) {
                    Text("这个术语的含义是什么？")
                        .font(.system(.headline, design: .monospaced))
                        .foregroundColor(.black.opacity(0.8))
                    
                    Text(term.term)
                        .font(.system(.largeTitle, design: .monospaced, weight: .bold))
                        .foregroundColor(.black)
                        .padding(.vertical, 16)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 0)
                                .stroke(Color.black, lineWidth: 2)
                                .background(Color.white)
                        )
                    
                    if showingAnswer {
                        VStack(spacing: 8) {
                            Text("正确答案：")
                                .font(.system(.body, design: .monospaced, weight: .semibold))
                                .foregroundColor(.black)
                            
                            Text(term.meaning)
                                .font(.system(.body, design: .monospaced))
                                .foregroundColor(.black.opacity(0.8))
                                .multilineTextAlignment(.center)
                                .lineSpacing(4)
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 0)
                                .fill(Color.black.opacity(0.05))
                                .stroke(Color.black.opacity(0.2), lineWidth: 1)
                        )
                    }
                }
                
                // 操作按钮
                VStack(spacing: 12) {
                    if !showingAnswer {
                        Button("显示答案") {
                            showingAnswer = true
                        }
                        .font(.system(.body, design: .monospaced, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 10)
                        .background(Color.black)
                    } else {
                        HStack(spacing: 16) {
                            Button("答错了") {
                                nextQuestion()
                            }
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(.black)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .stroke(Color.black, lineWidth: 1)
                                    .background(Color.white)
                            )
                            
                            Button("答对了") {
                                score += 1
                                nextQuestion()
                            }
                            .font(.system(.body, design: .monospaced, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(Color.black)
                        }
                    }
                }
            }
            
            Spacer()
            
            // 退出按钮
            Button("退出练习") {
                onExit()
            }
            .font(.system(.body, design: .monospaced))
            .foregroundColor(.black.opacity(0.6))
        }
        .padding(24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
        .sheet(isPresented: $showingResult) {
            PracticeResultView(
                score: score,
                total: terms.count,
                onRestart: {
                    currentIndex = 0
                    score = 0
                    showingResult = false
                },
                onExit: onExit
            )
        }
    }
    
    private func nextQuestion() {
        showingAnswer = false
        currentIndex += 1
        
        if currentIndex >= terms.count {
            showingResult = true
        }
    }
}

// 练习结果视图
struct PracticeResultView: View {
    let score: Int
    let total: Int
    let onRestart: () -> Void
    let onExit: () -> Void
    
    private var percentage: Int {
        guard total > 0 else { return 0 }
        return Int(Double(score) / Double(total) * 100)
    }
    
    var body: some View {
        VStack(spacing: 32) {
            VStack(spacing: 16) {
                Image(systemName: percentage >= 80 ? "star.fill" : "checkmark.circle")
                    .font(.system(size: 64, weight: .thin))
                    .foregroundColor(.black.opacity(0.6))
                
                Text("练习完成")
                    .font(.system(.title, design: .monospaced, weight: .bold))
                    .foregroundColor(.black)
            }
            
            VStack(spacing: 16) {
                Text("得分")
                    .font(.system(.headline, design: .monospaced))
                    .foregroundColor(.black.opacity(0.8))
                
                Text("\(score) / \(total)")
                    .font(.system(.largeTitle, design: .monospaced, weight: .bold))
                    .foregroundColor(.black)
                
                Text("\(percentage)%")
                    .font(.system(.title2, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
            }
            
            VStack(spacing: 12) {
                if percentage == 100 {
                    // 满分时：退出按钮在上方（黑色加粗框）
                    Button("退出") {
                        onExit()
                    }
                    .font(.system(.headline, design: .monospaced, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 12)
                    .background(Color.black)
                    
                    Button("再练一次") {
                        onRestart()
                    }
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
                } else {
                    // 非满分时：再练一次按钮在上方（黑色加粗框）
                    Button("再练一次") {
                        onRestart()
                    }
                    .font(.system(.headline, design: .monospaced, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 12)
                    .background(Color.black)
                    
                    Button("退出") {
                        onExit()
                    }
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
                }
            }
        }
        .padding(32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

#Preview {
    PracticeView()
        .modelContainer(for: [MusicTerm.self, Favorite.self])
}
