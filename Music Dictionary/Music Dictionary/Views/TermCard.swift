//
//  TermCard.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import SwiftUI
import SwiftData

// 基础术语卡片
struct TermCard: View {
    let term: MusicTerm
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    // 术语名称
                    Text(term.term)
                        .font(.system(.headline, design: .monospaced))
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    // 语言标识器
                    let languageTag = detectLanguage(term: term.term, meaning: term.meaning, pinyin: term.pinyin)
                    if !languageTag.isEmpty {
                        Text(languageTag)
                            .font(.system(.caption2, design: .monospaced))
                            .fontWeight(.bold)
                            .foregroundColor(.black.opacity(0.6))
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .stroke(Color.black.opacity(0.3), lineWidth: 1)
                            )
                    }
                }
                
                // 释义
                Text(term.meaning)
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.8))
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                
                // 拼音（如果有）
                if !term.pinyin.isEmpty {
                    Text(truncatedPinyin(term.pinyin))
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.5))
                        .italic()
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 搜索结果卡片
struct SearchResultCard: View {
    let result: SearchResult
    let searchText: String
    let index: Int
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    // 序号
                    Text("\(index)")
                        .font(.system(.caption, design: .monospaced))
                        .fontWeight(.bold)
                        .foregroundColor(.black.opacity(0.4))
                        .frame(width: 20, alignment: .leading)
                    
                    VStack(alignment: .leading, spacing: 6) {
                        HStack {
                            // 高亮显示的术语名称
                            HighlightedText(
                                text: result.term.term,
                                highlight: searchText,
                                font: .system(.headline, design: .monospaced),
                                highlightColor: .black,
                                normalColor: .black.opacity(0.7)
                            )
                            
                            Spacer()
                            
                            // 匹配度和类型
                            HStack(spacing: 4) {
                                Text("\(Int(result.score * 100))%")
                                    .font(.system(.caption2, design: .monospaced))
                                    .fontWeight(.bold)
                                    .foregroundColor(.black.opacity(0.6))
                                
                                Text(result.matchType.displayName)
                                    .font(.system(.caption2, design: .monospaced))
                                    .foregroundColor(.black.opacity(0.4))
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .stroke(Color.black.opacity(0.2), lineWidth: 1)
                            )
                        }
                        
                        // 高亮显示的释义
                        HighlightedText(
                            text: result.term.meaning,
                            highlight: searchText,
                            font: .system(.body, design: .monospaced),
                            highlightColor: .black,
                            normalColor: .black.opacity(0.8)
                        )
                        .lineLimit(2)
                        
                        // 拼音（如果匹配）
                        if !result.term.pinyin.isEmpty && result.matchType == .pinyin {
                            HighlightedText(
                                text: result.term.pinyin,
                                highlight: searchText,
                                font: .system(.caption, design: .monospaced),
                                highlightColor: .black.opacity(0.7),
                                normalColor: .black.opacity(0.5)
                            )
                            .italic()
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .overlay(
                Rectangle()
                    .fill(Color.black.opacity(0.1))
                    .frame(height: 1),
                alignment: .bottom
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 高亮文本组件
struct HighlightedText: View {
    let text: String
    let highlight: String
    let font: Font
    let highlightColor: Color
    let normalColor: Color
    
    var body: some View {
        let attributedString = createAttributedString()
        
        Text(AttributedString(attributedString))
            .font(font)
    }
    
    private func createAttributedString() -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: text)
        let range = NSRange(location: 0, length: text.count)
        
        // 设置默认颜色
        attributedString.addAttribute(.foregroundColor, value: UIColor(normalColor), range: range)
        
        // 查找并高亮匹配的文本
        let lowercaseText = text.lowercased()
        let lowercaseHighlight = highlight.lowercased()
        if lowercaseHighlight.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return attributedString
        }
        
        var searchRange = NSRange(location: 0, length: lowercaseText.count)
        
        while searchRange.location < lowercaseText.count {
            let foundRange = (lowercaseText as NSString).range(of: lowercaseHighlight, options: [], range: searchRange)
            
            if foundRange.location == NSNotFound {
                break
            }
            
            // 高亮找到的文本
            attributedString.addAttribute(.foregroundColor, value: UIColor(highlightColor), range: foundRange)
            attributedString.addAttribute(.font, value: UIFont.monospacedSystemFont(ofSize: 16, weight: .bold), range: foundRange)
            
            // 更新搜索范围
            searchRange.location = foundRange.location + foundRange.length
            searchRange.length = lowercaseText.count - searchRange.location
        }
        
        return attributedString
    }
}

// 术语详情视图
struct TermDetailView: View {
    let term: MusicTerm
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @State private var isFavorited = false
    @State private var showingFavoriteAnimation = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 术语标题
                    VStack(alignment: .leading, spacing: 8) {
                        Text(term.term)
                            .font(.system(.largeTitle, design: .monospaced))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                        
                        if !term.pinyin.isEmpty {
                            Text(term.pinyin)
                                .font(.system(.title3, design: .monospaced))
                                .foregroundColor(.black.opacity(0.6))
                                .italic()
                        }
                    }
                    
                    // 分割线
                    Rectangle()
                        .fill(Color.black)
                        .frame(height: 2)
                    
                    // 释义
                    VStack(alignment: .leading, spacing: 8) {
                        Text("释义")
                            .font(.system(.headline, design: .monospaced))
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                        
                        Text(term.meaning)
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(.black.opacity(0.8))
                            .lineSpacing(4)
                    }
                    
                    // 分类信息
                    if !term.category.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("分类")
                                .font(.system(.headline, design: .monospaced))
                                .fontWeight(.semibold)
                                .foregroundColor(.black)
                            
                            Text(term.category)
                                .font(.system(.body, design: .monospaced))
                                .foregroundColor(.black.opacity(0.8))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 0)
                                        .stroke(Color.black.opacity(0.3), lineWidth: 1)
                                )
                        }
                    }
                    
                    Spacer()
                }
                .padding(24)
            }
            .navigationTitle("术语详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: toggleFavorite) {
                        Image(systemName: isFavorited ? "heart.fill" : "heart")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                            .scaleEffect(showingFavoriteAnimation ? 1.3 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: showingFavoriteAnimation)
                    }
                }
            }
            .background(Color.white)
        }
        .onAppear {
            checkIfFavorited()
        }
    }
    
    private func checkIfFavorited() {
        let termId = term.id
        let descriptor = FetchDescriptor<Favorite>(
            predicate: #Predicate<Favorite> { favorite in
                favorite.termId == termId
            }
        )

        do {
            let favorites = try modelContext.fetch(descriptor)
            isFavorited = !favorites.isEmpty
        } catch {
            print("检查收藏状态失败: \(error)")
        }
    }
    
    private func toggleFavorite() {
        if isFavorited {
            removeFavorite()
        } else {
            addFavorite()
        }
        
        showingFavoriteAnimation = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            showingFavoriteAnimation = false
        }
    }
    
    private func addFavorite() {
        let favorite = Favorite.from(musicTerm: term)
        modelContext.insert(favorite)
        
        do {
            try modelContext.save()
            isFavorited = true
        } catch {
            print("添加收藏失败: \(error)")
        }
    }
    
    private func removeFavorite() {
        let termId = term.id
        let descriptor = FetchDescriptor<Favorite>(
            predicate: #Predicate<Favorite> { favorite in
                favorite.termId == termId
            }
        )

        do {
            let favorites = try modelContext.fetch(descriptor)
            for favorite in favorites {
                modelContext.delete(favorite)
            }
            try modelContext.save()
            isFavorited = false
        } catch {
            print("移除收藏失败: \(error)")
        }
    }
}

// 拼音截断函数
private func truncatedPinyin(_ pinyin: String) -> String {
    // 计算字母数量（不包括标点符号）
    let letterCount = pinyin.filter { $0.isLetter }.count

    if letterCount <= 19 {
        return pinyin
    }

    // 截断到19个字母，然后添加省略号
    var truncated = ""
    var letterCounter = 0

    for char in pinyin {
        if char.isLetter {
            letterCounter += 1
            if letterCounter > 19 {
                break
            }
        }
        truncated.append(char)
    }

    return truncated + "..."
}

// 去除重音/兼容全角与大小写
private func stripDiacritics(_ s: String) -> String {
    s.folding(options: [.diacriticInsensitive, .widthInsensitive, .caseInsensitive], locale: .current)
}

// 常见缩写展开（仅当整词或末尾带点的缩写）
private func expandAbbrev(_ s: String) -> String {
    let lowered = s.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
    let noDot = lowered.trimmingCharacters(in: CharacterSet(charactersIn: "."))
    let map: [String: String] = [
        "rit": "ritardando",
        "riten": "ritenuto",
        "accel": "accelerando",
        "cresc": "crescendo",
        "decresc": "decrescendo",
        "dim": "diminuendo",
        "pizz": "pizzicato",
        "legg": "leggiero",
        "stacc": "staccato",
        "sord": "sordino",
        "trem": "tremolo",
        "vib": "vibrato",
        "div": "divisi"
    ]
    if let full = map[noDot] { return full }
    return s
}

// 记谱/力度等非语言标记
private func isNotation(_ s: String) -> Bool {
    // 含有乐谱符号直接视为记谱
    let symbolSet = CharacterSet(charactersIn: "♩♪♫♬♭♯𝄪𝄫𝄞𝄢")
    if s.rangeOfCharacter(from: symbolSet) != nil { return true }

    // 常见力度/回头/八度标记（忽略空格与句点）
    let t = s.lowercased()
        .replacingOccurrences(of: " ", with: "")
        .replacingOccurrences(of: ".", with: "")
    let tokens: Set<String> = [
        "p","pp","ppp","f","ff","fff","mp","mf",
        "sfz","sffz","fp","fpp","tr","8va","8vb","8ve",
        "dc","ds","fine","coda","segno","tacet"
    ]
    return tokens.contains(t)
}

// 结合释义/拼音的提示进行优先判定
private func detectLanguage(term: String, meaning: String?, pinyin: String?) -> String {
    // 1) 释义里出现“（德语）/德语/deyu”等字样时，直接标为 DE
    if let m = meaning?.lowercased() {
        if m.contains("德语") || m.contains("(德语)") || m.contains("（德语）") || m.contains("deyu") {
            return "DE"
        }
        if m.contains("英语") || m.contains("(英语)") || m.contains("（英语）") || m.contains("yingyu") {
            return "EN"
        }
        if m.contains("意大利语") || m.contains("(意大利语)") || m.contains("（意大利语）") || m.contains("yidaliyu") {
            return "IT"
        }
        if m.contains("法语") || m.contains("(法语)") || m.contains("（法语）") || m.contains("fayu") {
            return "FR"
        }
    }
    // 2) 拼音字段里有“（deyu）/deyu”等提示
    if let p = pinyin?.lowercased() {
        if p.contains("（deyu）") || p.contains("(deyu)") || p.contains("deyu") { return "DE" }
        if p.contains("（yingyu）") || p.contains("(yingyu)") || p.contains("yingyu") { return "EN" }
        if p.contains("（yidaliyu）") || p.contains("(yidaliyu)") || p.contains("yidaliyu") { return "IT" }
        if p.contains("（fayu）") || p.contains("(fayu)") || p.contains("fayu") { return "FR" }
    }
    // 3) 回落到核心检测
    return detectLanguage(term)
}

// 智能语言检测函数（加强版）
private func detectLanguage(_ term: String) -> String {
    let raw = term.trimmingCharacters(in: .whitespacesAndNewlines)
    if raw.isEmpty { return "" }

    // 中文：优先返回
    if raw.contains(where: { $0.unicodeScalars.contains { $0.value >= 0x4E00 && $0.value <= 0x9FFF } }) {
        return "中"
    }

    // 展开缩写 & 记谱优先
    let expanded = expandAbbrev(raw)
    if isNotation(expanded) { return "记谱" }

    // 归一化（同时保留带重音与去重音两条线索）
    let lower = expanded.lowercased()
    let folded = stripDiacritics(lower)

    // 分词（字母与撇号视为词的一部分）
    let tokenSplitter: (Character) -> Bool = { !$0.isLetter && $0 != "'" }
    let tokens = lower.split(whereSeparator: tokenSplitter).map(String.init)
    let fTokens = folded.split(whereSeparator: tokenSplitter).map(String.init)
    let tokenSet = Set(tokens)
    let fTokenSet = Set(fTokens)


    // 语言特征集合
    let itWords: Set<String> = [
        "allegro","andante","adagio","largo","presto","vivace","moderato","grave",
        "cantabile","legato","staccato","pizzicato","tremolo","vibrato","marcato","tenuto",
        "crescendo","diminuendo","sforzando","ritardando","ritenuto","accelerando",
        "dolce","giocoso","agitato","appassionato","grazioso","sempre","subito",
        "poco","molto","meno","più","piu","con","senza","brillante","scherzando",
        "arpeggio","portamento","glissando","sordino","divisi","brio"
    ]
    let itSuffixes = ["ando","endo","mente","zione","issimo","issima","etto","etta","oso","osa","ato","ata","ito","ita"]

    let frWords: Set<String> = [
        "avec","sans","très","tres","peu","très peu","un peu","vite","lent",
        "doucement","retenu","élargi","elargi","serré","serre","pressé","presse",
        "largeur","chaleur"
    ]

    let deWords: Set<String> = [
        "mit","ohne","sehr","nicht","ruhig","schnell","langsam","etwas","immer",
        "bewegt","leidenschaftlich","kräftig","kraftig","zart","kräftig"
    ]
    let deSuffixes = ["ung","keit","heit","lich","isch"]

    let enWords: Set<String> = [
        // 基础
        "with","without","very","more","less","fast","slow","loud","soft",
        "major","minor","sharp","flat","natural","chord","scale","key","time",
        "beat","rhythm","melody","harmony","bass","treble","sustain","mute",
        // 乐理常见
        "counterpoint","cadence","modulation","inversion","suspension","anticipation",
        "neighbor","passing","parallel","similar","contrary","motion",
        // 你提到的术语
        "voice","leading","balance","crossing","exchange","separation"
    ]

    let laWords: Set<String> = ["ad","ab","ex","in","per","sub","cum","sine","libitum","ad libitum","perpetuum"]

    // 德语重音字母强信号
    if raw.rangeOfCharacter(from: CharacterSet(charactersIn: "äöüß")) != nil {
        return "DE"
    }
    // 法语重音字母强信号
    if raw.rangeOfCharacter(from: CharacterSet(charactersIn: "éèêàâîôûç")) != nil {
        return "FR"
    }

    // 英文 Voice-* 系列优先识别
    if tokenSet.contains("voice") { return "EN" }

    // （放到重音强信号之后，避免把 ä/ö/ü/ß 等误判为“其他”）
    let hasNonLatin = raw.contains { ch in
        guard let v = ch.unicodeScalars.first?.value else { return false }
        return v > 127 && !(v >= 0x4E00 && v <= 0x9FFF)
    }
    if hasNonLatin { return "其他" }

    // 规则：功能词/词典命中
    if !itWords.isDisjoint(with: tokenSet) || !itWords.isDisjoint(with: fTokenSet) {
        return "IT"
    }
    if !frWords.isDisjoint(with: tokenSet) || !frWords.isDisjoint(with: fTokenSet) {
        return "FR"
    }
    if !deWords.isDisjoint(with: tokenSet) || !deWords.isDisjoint(with: fTokenSet) {
        return "DE"
    }
    if !enWords.isDisjoint(with: tokenSet) {
        return "EN"
    }
    if !laWords.isDisjoint(with: tokenSet) || folded.contains("libitum") {
        return "LA"
    }

    // 词尾启发式（仅在无明确词典命中时）
    if itSuffixes.contains(where: { folded.hasSuffix($0) }) { return "IT" }
    if deSuffixes.contains(where: { folded.hasSuffix($0) }) { return "DE" }

    // 默认未知（拉丁字母但没把握，不显示标签）
    return ""
}

#Preview {
    let sampleTerm = MusicTerm(
        term: "Dolce",
        meaning: "甜美的，柔和的",
        pinyin: "tianmei",
        category: "表情术语"
    )

    VStack {
        TermCard(term: sampleTerm) {}

        SearchResultCard(
            result: SearchResult(
                term: sampleTerm,
                score: 0.95,
                matchType: .exact
            ),
            searchText: "dolce",
            index: 1
        ) {}
    }
    .padding()
    .background(Color.white)
}
