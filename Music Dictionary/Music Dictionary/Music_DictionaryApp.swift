//
//  Music_DictionaryApp.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import SwiftUI
import SwiftData

@main
struct Music_DictionaryApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            MusicTerm.self,
            Favorite.self
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .modelContainer(sharedModelContainer)
    }
}
