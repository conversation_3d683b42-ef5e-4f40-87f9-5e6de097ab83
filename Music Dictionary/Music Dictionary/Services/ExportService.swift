//
//  ExportService.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import Foundation
import UIKit

// 导出格式枚举
enum ExportFormat: String, CaseIterable {
    case text = "text"
    case json = "json"
    case csv = "csv"
    
    var displayName: String {
        switch self {
        case .text: return "文本格式"
        case .json: return "JSON格式"
        case .csv: return "CSV格式"
        }
    }
    
    var fileExtension: String {
        switch self {
        case .text: return "txt"
        case .json: return "json"
        case .csv: return "csv"
        }
    }
    
    var mimeType: String {
        switch self {
        case .text: return "text/plain"
        case .json: return "application/json"
        case .csv: return "text/csv"
        }
    }
}

// 导出数据类型
enum ExportDataType {
    case allTerms([MusicTerm])
    case favorites([Favorite])
}

@Observable
class ExportService {
    private(set) var isExporting = false
    private(set) var exportProgress: Double = 0
    private(set) var errorMessage: String?
    
    // 导出数据
    func exportData(
        _ dataType: ExportDataType,
        format: ExportFormat,
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        isExporting = true
        exportProgress = 0
        errorMessage = nil
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            do {
                let content = try self?.generateContent(dataType, format: format) ?? ""
                let url = try self?.saveToFile(content: content, format: format, dataType: dataType) ?? URL(fileURLWithPath: "")
                
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 1.0
                    completion(.success(url))
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.errorMessage = error.localizedDescription
                    completion(.failure(error))
                }
            }
        }
    }
    
    // 生成导出内容
    private func generateContent(_ dataType: ExportDataType, format: ExportFormat) throws -> String {
        switch format {
        case .text:
            return try generateTextContent(dataType)
        case .json:
            return try generateJSONContent(dataType)
        case .csv:
            return try generateCSVContent(dataType)
        }
    }
    
    // 生成文本格式内容
    private func generateTextContent(_ dataType: ExportDataType) throws -> String {
        var content = ""
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .short
        
        switch dataType {
        case .allTerms(let terms):
            content += "音乐术语词典\n"
            content += "导出时间: \(dateFormatter.string(from: Date()))\n"
            content += "术语总数: \(terms.count)\n"
            content += String(repeating: "=", count: 50) + "\n\n"
            
            for (index, term) in terms.enumerated() {
                content += "\(index + 1). \(term.term)\n"
                content += "   释义: \(term.meaning)\n"
                if !term.pinyin.isEmpty {
                    content += "   拼音: \(term.pinyin)\n"
                }
                if !term.category.isEmpty {
                    content += "   分类: \(term.category)\n"
                }
                content += "\n"
            }
            
        case .favorites(let favorites):
            content += "收藏的音乐术语\n"
            content += "导出时间: \(dateFormatter.string(from: Date()))\n"
            content += "收藏总数: \(favorites.count)\n"
            content += String(repeating: "=", count: 50) + "\n\n"
            
            for (index, favorite) in favorites.enumerated() {
                content += "\(index + 1). \(favorite.term)\n"
                content += "   释义: \(favorite.meaning)\n"
                content += "   收藏时间: \(dateFormatter.string(from: favorite.createdAt))\n"
                if !favorite.notes.isEmpty {
                    content += "   笔记: \(favorite.notes)\n"
                }
                content += "\n"
            }
        }
        
        return content
    }
    
    // 生成JSON格式内容
    private func generateJSONContent(_ dataType: ExportDataType) throws -> String {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        encoder.dateEncodingStrategy = .iso8601
        
        let data: Data
        
        switch dataType {
        case .allTerms(let terms):
            let exportData = ExportedTerms(
                exportDate: Date(),
                totalCount: terms.count,
                terms: terms.map { ExportedTerm(from: $0) }
            )
            data = try encoder.encode(exportData)
            
        case .favorites(let favorites):
            let exportData = ExportedFavorites(
                exportDate: Date(),
                totalCount: favorites.count,
                favorites: favorites.map { ExportedFavorite(from: $0) }
            )
            data = try encoder.encode(exportData)
        }
        
        guard let jsonString = String(data: data, encoding: .utf8) else {
            throw ExportError.encodingFailed
        }
        
        return jsonString
    }
    
    // 生成CSV格式内容
    private func generateCSVContent(_ dataType: ExportDataType) throws -> String {
        var content = ""
        
        switch dataType {
        case .allTerms(let terms):
            content += "术语,释义,拼音,分类,创建时间\n"
            for term in terms {
                let row = [
                    escapeCSVField(term.term),
                    escapeCSVField(term.meaning),
                    escapeCSVField(term.pinyin),
                    escapeCSVField(term.category),
                    escapeCSVField(ISO8601DateFormatter().string(from: term.createdAt))
                ].joined(separator: ",")
                content += row + "\n"
            }
            
        case .favorites(let favorites):
            content += "术语,释义,收藏时间,笔记\n"
            for favorite in favorites {
                let row = [
                    escapeCSVField(favorite.term),
                    escapeCSVField(favorite.meaning),
                    escapeCSVField(ISO8601DateFormatter().string(from: favorite.createdAt)),
                    escapeCSVField(favorite.notes)
                ].joined(separator: ",")
                content += row + "\n"
            }
        }
        
        return content
    }
    
    // CSV字段转义
    private func escapeCSVField(_ field: String) -> String {
        if field.contains(",") || field.contains("\"") || field.contains("\n") {
            return "\"" + field.replacingOccurrences(of: "\"", with: "\"\"") + "\""
        }
        return field
    }
    
    // 保存到文件
    private func saveToFile(content: String, format: ExportFormat, dataType: ExportDataType) throws -> URL {
        let fileName = generateFileName(format: format, dataType: dataType)
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileURL = documentsPath.appendingPathComponent(fileName)
        
        try content.write(to: fileURL, atomically: true, encoding: .utf8)
        
        return fileURL
    }
    
    // 生成文件名
    private func generateFileName(format: ExportFormat, dataType: ExportDataType) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = dateFormatter.string(from: Date())
        
        let prefix: String
        switch dataType {
        case .allTerms: prefix = "音乐术语词典"
        case .favorites: prefix = "收藏术语"
        }
        
        return "\(prefix)_\(timestamp).\(format.fileExtension)"
    }
    
    // 清除错误
    func clearError() {
        errorMessage = nil
    }
}

// 导出错误类型
enum ExportError: LocalizedError {
    case encodingFailed
    case fileWriteFailed
    case unsupportedFormat
    
    var errorDescription: String? {
        switch self {
        case .encodingFailed:
            return "数据编码失败"
        case .fileWriteFailed:
            return "文件写入失败"
        case .unsupportedFormat:
            return "不支持的导出格式"
        }
    }
}

// 导出数据结构
struct ExportedTerms: Codable {
    let exportDate: Date
    let totalCount: Int
    let terms: [ExportedTerm]
}

struct ExportedTerm: Codable {
    let term: String
    let meaning: String
    let pinyin: String
    let category: String
    let createdAt: Date
    
    init(from musicTerm: MusicTerm) {
        self.term = musicTerm.term
        self.meaning = musicTerm.meaning
        self.pinyin = musicTerm.pinyin
        self.category = musicTerm.category
        self.createdAt = musicTerm.createdAt
    }
}

struct ExportedFavorites: Codable {
    let exportDate: Date
    let totalCount: Int
    let favorites: [ExportedFavorite]
}

struct ExportedFavorite: Codable {
    let term: String
    let meaning: String
    let notes: String
    let createdAt: Date
    
    init(from favorite: Favorite) {
        self.term = favorite.term
        self.meaning = favorite.meaning
        self.notes = favorite.notes
        self.createdAt = favorite.createdAt
    }
}
