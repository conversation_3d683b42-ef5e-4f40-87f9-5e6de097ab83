# 智能语言识别算法 🧠

## 问题分析

**原问题**：之前简单用ASCII判断是否为"英文"，但音乐术语来源复杂：
- 🇮🇹 **意大利语**：Dolce, Allegro, Andante, Cantabile...
- 🇫🇷 **法语**：Avec passion, <PERSON><PERSON>, Très vite...
- 🇩🇪 **德语**：Ausdrucksvoll, Lebhaft, Mit Ausdruck...
- 🇱🇦 **拉丁语**：Ad libitum, Ab initio, Per aspera...
- 🇬🇧 **英语**：Fast, Slow, Major, Minor...
- 🇨🇳 **中文**：快板，慢板，强，弱...

## 新算法设计

### 🎯 检测优先级
1. **中文** → 最容易识别（Unicode范围）
2. **意大利语** → 音乐术语最常见来源
3. **法语** → 第二常见
4. **德语** → 古典音乐传统
5. **拉丁语** → 学术音乐术语
6. **英语** → 现代音乐术语
7. **其他语言** → 特殊字符
8. **未知** → 不显示标签

### 🔍 检测方法

#### 1. 中文检测
```swift
// Unicode范围：0x4E00-0x9FFF (CJK统一汉字)
if term.contains { $0.unicodeScalars.contains { $0.value >= 0x4E00 && $0.value <= 0x9FFF } } {
    return "中"
}
```

#### 2. 意大利语检测 (IT)
**词汇库匹配**：
- 速度术语：dolce, forte, piano, allegro, andante, adagio, largo, presto
- 演奏技巧：cantabile, legato, staccato, pizzicato, tremolo, vibrato
- 力度术语：crescendo, diminuendo, sforzando, ritardando, accelerando
- 表情术语：maestoso, grazioso, scherzando, appassionato, affettuoso
- 结构术语：cadenza, coda, fine, segno, capo

**语言特征匹配**：
- 常见结尾：-ando, -endo, -oso, -ato, -etto, -ino, -imo
- 常见词汇：con, alla, del, poco, molto, più, meno
- 字母组合：gn, gl, sc, -zione

#### 3. 法语检测 (FR)
**词汇库**：avec, sans, très, peu, doux, fort, vite, lent, chaleur, passion, grâce, largeur, éclat, élan, émotion

**语言特征**：
- 常见词：avec, sans, très, peu, plus, moins
- 常见结尾：-eur, -euse, -ment, -tion, -sion

#### 4. 德语检测 (DE)
**词汇库**：mit, ohne, sehr, wenig, stark, schwach, schnell, langsam, ausdruck, ausdrucksvoll, lebhaft, ruhig, zart, kräftig

**语言特征**：
- 常见结尾：-ung, -keit, -heit, -lich, -isch, -voll

#### 5. 拉丁语检测 (LA)
**词汇库**：ad, ab, ex, in, per, sub, cum, sine, libitum, infinitum, perpetuum, aeternum

#### 6. 英语检测 (EN)
**严格匹配**：只有明确的英语音乐术语才标记
- 基础词汇：the, and, or, with, without, very, more, less
- 音乐术语：fast, slow, loud, soft, high, low, major, minor, sharp, flat, natural, chord, scale, key, time, beat, rhythm, melody, harmony, bass, treble

#### 7. 其他语言检测
```swift
// 包含非拉丁字符但不是中文
let hasNonLatinChars = term.contains { char in
    let scalar = char.unicodeScalars.first?.value ?? 0
    return scalar > 127 && !(scalar >= 0x4E00 && scalar <= 0x9FFF)
}
```

## 算法特点

### ✅ 优势
1. **准确性高**：基于音乐术语的实际语言分布
2. **覆盖面广**：涵盖音乐术语的主要来源语言
3. **智能判断**：不是简单的字符判断，而是语言学特征
4. **保守策略**：不确定时不显示标签，避免错误标记

### 🎯 设计原则
1. **意大利语优先**：音乐术语最主要来源
2. **严格英语判断**：避免将其他语言误判为英语
3. **特征组合**：词汇+语言特征双重验证
4. **渐进匹配**：从最确定到最不确定

### 📊 预期效果
- **Dolce** → IT (意大利语)
- **Avec passion** → FR (法语)
- **Ausdrucksvoll** → DE (德语)
- **Ad libitum** → LA (拉丁语)
- **Major scale** → EN (英语)
- **快板** → 中 (中文)
- **不确定的术语** → 无标签

## 测试用例

### 🇮🇹 意大利语术语
- ✅ Allegro → IT
- ✅ Dolce → IT  
- ✅ Cantabile → IT
- ✅ Pizzicato → IT
- ✅ Crescendo → IT

### 🇫🇷 法语术语
- ✅ Avec passion → FR
- ✅ Avec grâce → FR
- ✅ Très vite → FR

### 🇩🇪 德语术语
- ✅ Ausdrucksvoll → DE
- ✅ Mit Ausdruck → DE

### 🇱🇦 拉丁语术语
- ✅ Ad libitum → LA
- ✅ Ab initio → LA

### 🇬🇧 英语术语
- ✅ Major scale → EN
- ✅ Fast tempo → EN

### 🇨🇳 中文术语
- ✅ 快板 → 中
- ✅ 柔和的 → 中

### ❓ 不确定术语
- ✅ Aentoq → 无标签 (不确定语言)
- ✅ 随意的术语 → 无标签 (避免误判)

## 维护和扩展

### 🔧 如何添加新语言
1. 在 `detectLanguage()` 函数中添加新的检测逻辑
2. 定义该语言的词汇库和特征模式
3. 确定合适的标签缩写
4. 添加到检测优先级中

### 📈 如何优化准确性
1. 扩展各语言的词汇库
2. 添加更多语言特征模式
3. 根据实际使用情况调整检测优先级
4. 收集用户反馈进行算法调优

这个算法现在能够智能识别音乐术语的真实语言来源，而不是简单粗暴地判断为"英文"！🎵
