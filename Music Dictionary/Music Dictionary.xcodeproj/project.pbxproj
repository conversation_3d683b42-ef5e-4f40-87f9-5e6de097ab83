// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		C4CA1F382E5C92000057C2F2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C4CA1F202E5C92000057C2F2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C4CA1F272E5C92000057C2F2;
			remoteInfo = "Music Dictionary";
		};
		C4CA1F422E5C92000057C2F2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C4CA1F202E5C92000057C2F2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C4CA1F272E5C92000057C2F2;
			remoteInfo = "Music Dictionary";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		C4CA1F282E5C92000057C2F2 /* Music Dictionary.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Music Dictionary.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		C4CA1F372E5C92000057C2F2 /* Music DictionaryTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Music DictionaryTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		C4CA1F412E5C92000057C2F2 /* Music DictionaryUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Music DictionaryUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C4CA1F2A2E5C92000057C2F2 /* Music Dictionary */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Music Dictionary";
			sourceTree = "<group>";
		};
		C4CA1F3A2E5C92000057C2F2 /* Music DictionaryTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Music DictionaryTests";
			sourceTree = "<group>";
		};
		C4CA1F442E5C92000057C2F2 /* Music DictionaryUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Music DictionaryUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C4CA1F252E5C92000057C2F2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4CA1F342E5C92000057C2F2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4CA1F3E2E5C92000057C2F2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C4CA1F1F2E5C92000057C2F2 = {
			isa = PBXGroup;
			children = (
				C4CA1F2A2E5C92000057C2F2 /* Music Dictionary */,
				C4CA1F3A2E5C92000057C2F2 /* Music DictionaryTests */,
				C4CA1F442E5C92000057C2F2 /* Music DictionaryUITests */,
				C4CA1F292E5C92000057C2F2 /* Products */,
			);
			sourceTree = "<group>";
		};
		C4CA1F292E5C92000057C2F2 /* Products */ = {
			isa = PBXGroup;
			children = (
				C4CA1F282E5C92000057C2F2 /* Music Dictionary.app */,
				C4CA1F372E5C92000057C2F2 /* Music DictionaryTests.xctest */,
				C4CA1F412E5C92000057C2F2 /* Music DictionaryUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C4CA1F272E5C92000057C2F2 /* Music Dictionary */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C4CA1F4B2E5C92000057C2F2 /* Build configuration list for PBXNativeTarget "Music Dictionary" */;
			buildPhases = (
				C4CA1F242E5C92000057C2F2 /* Sources */,
				C4CA1F252E5C92000057C2F2 /* Frameworks */,
				C4CA1F262E5C92000057C2F2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C4CA1F2A2E5C92000057C2F2 /* Music Dictionary */,
			);
			name = "Music Dictionary";
			packageProductDependencies = (
			);
			productName = "Music Dictionary";
			productReference = C4CA1F282E5C92000057C2F2 /* Music Dictionary.app */;
			productType = "com.apple.product-type.application";
		};
		C4CA1F362E5C92000057C2F2 /* Music DictionaryTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C4CA1F4E2E5C92000057C2F2 /* Build configuration list for PBXNativeTarget "Music DictionaryTests" */;
			buildPhases = (
				C4CA1F332E5C92000057C2F2 /* Sources */,
				C4CA1F342E5C92000057C2F2 /* Frameworks */,
				C4CA1F352E5C92000057C2F2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C4CA1F392E5C92000057C2F2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C4CA1F3A2E5C92000057C2F2 /* Music DictionaryTests */,
			);
			name = "Music DictionaryTests";
			packageProductDependencies = (
			);
			productName = "Music DictionaryTests";
			productReference = C4CA1F372E5C92000057C2F2 /* Music DictionaryTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		C4CA1F402E5C92000057C2F2 /* Music DictionaryUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C4CA1F512E5C92000057C2F2 /* Build configuration list for PBXNativeTarget "Music DictionaryUITests" */;
			buildPhases = (
				C4CA1F3D2E5C92000057C2F2 /* Sources */,
				C4CA1F3E2E5C92000057C2F2 /* Frameworks */,
				C4CA1F3F2E5C92000057C2F2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C4CA1F432E5C92000057C2F2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C4CA1F442E5C92000057C2F2 /* Music DictionaryUITests */,
			);
			name = "Music DictionaryUITests";
			packageProductDependencies = (
			);
			productName = "Music DictionaryUITests";
			productReference = C4CA1F412E5C92000057C2F2 /* Music DictionaryUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C4CA1F202E5C92000057C2F2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					C4CA1F272E5C92000057C2F2 = {
						CreatedOnToolsVersion = 26.0;
					};
					C4CA1F362E5C92000057C2F2 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = C4CA1F272E5C92000057C2F2;
					};
					C4CA1F402E5C92000057C2F2 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = C4CA1F272E5C92000057C2F2;
					};
				};
			};
			buildConfigurationList = C4CA1F232E5C92000057C2F2 /* Build configuration list for PBXProject "Music Dictionary" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C4CA1F1F2E5C92000057C2F2;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = C4CA1F292E5C92000057C2F2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C4CA1F272E5C92000057C2F2 /* Music Dictionary */,
				C4CA1F362E5C92000057C2F2 /* Music DictionaryTests */,
				C4CA1F402E5C92000057C2F2 /* Music DictionaryUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C4CA1F262E5C92000057C2F2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4CA1F352E5C92000057C2F2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4CA1F3F2E5C92000057C2F2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C4CA1F242E5C92000057C2F2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4CA1F332E5C92000057C2F2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4CA1F3D2E5C92000057C2F2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C4CA1F392E5C92000057C2F2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C4CA1F272E5C92000057C2F2 /* Music Dictionary */;
			targetProxy = C4CA1F382E5C92000057C2F2 /* PBXContainerItemProxy */;
		};
		C4CA1F432E5C92000057C2F2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C4CA1F272E5C92000057C2F2 /* Music Dictionary */;
			targetProxy = C4CA1F422E5C92000057C2F2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		C4CA1F492E5C92000057C2F2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C4CA1F4A2E5C92000057C2F2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C4CA1F4C2E5C92000057C2F2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "me.yuzeguitar.Music-Dictionary";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C4CA1F4D2E5C92000057C2F2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "me.yuzeguitar.Music-Dictionary";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C4CA1F4F2E5C92000057C2F2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "me.yuzeguitar.Music-DictionaryTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Music Dictionary.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Music Dictionary";
			};
			name = Debug;
		};
		C4CA1F502E5C92000057C2F2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "me.yuzeguitar.Music-DictionaryTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Music Dictionary.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Music Dictionary";
			};
			name = Release;
		};
		C4CA1F522E5C92000057C2F2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "me.yuzeguitar.Music-DictionaryUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Music Dictionary";
			};
			name = Debug;
		};
		C4CA1F532E5C92000057C2F2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QJ32AL6GFG;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "me.yuzeguitar.Music-DictionaryUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Music Dictionary";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C4CA1F232E5C92000057C2F2 /* Build configuration list for PBXProject "Music Dictionary" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4CA1F492E5C92000057C2F2 /* Debug */,
				C4CA1F4A2E5C92000057C2F2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C4CA1F4B2E5C92000057C2F2 /* Build configuration list for PBXNativeTarget "Music Dictionary" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4CA1F4C2E5C92000057C2F2 /* Debug */,
				C4CA1F4D2E5C92000057C2F2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C4CA1F4E2E5C92000057C2F2 /* Build configuration list for PBXNativeTarget "Music DictionaryTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4CA1F4F2E5C92000057C2F2 /* Debug */,
				C4CA1F502E5C92000057C2F2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C4CA1F512E5C92000057C2F2 /* Build configuration list for PBXNativeTarget "Music DictionaryUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4CA1F522E5C92000057C2F2 /* Debug */,
				C4CA1F532E5C92000057C2F2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = C4CA1F202E5C92000057C2F2 /* Project object */;
}
