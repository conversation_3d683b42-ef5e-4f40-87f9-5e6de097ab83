#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将音乐术语数据文件夹中的所有ODS文件转换为CSV格式
"""

import os
import pandas as pd
from pathlib import Path
import sys

def convert_ods_to_csv(ods_file_path, csv_file_path):
    """
    将单个ODS文件转换为CSV文件
    
    Args:
        ods_file_path (str): ODS文件路径
        csv_file_path (str): 输出CSV文件路径
    """
    try:
        print(f"正在转换: {ods_file_path}")
        
        # 读取ODS文件
        df = pd.read_excel(ods_file_path, engine='odf')
        
        # 保存为CSV文件，使用UTF-8编码
        df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')
        
        print(f"转换完成: {csv_file_path}")
        print(f"数据行数: {len(df)}, 列数: {len(df.columns)}")
        
        # 显示前几行数据预览
        if not df.empty:
            print("数据预览:")
            print(df.head(3).to_string())
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"转换失败 {ods_file_path}: {str(e)}")
        return False

def main():
    """
    主函数：批量转换所有ODS文件
    """
    # 设置文件夹路径
    ods_folder = "音乐术语数据"
    csv_folder = "音乐术语数据_CSV"
    
    # 创建输出文件夹
    Path(csv_folder).mkdir(exist_ok=True)
    
    # 获取所有ODS文件
    ods_files = []
    if os.path.exists(ods_folder):
        for file in os.listdir(ods_folder):
            if file.endswith('.ods'):
                ods_files.append(file)
    
    if not ods_files:
        print(f"在 {ods_folder} 文件夹中没有找到ODS文件")
        return
    
    print(f"找到 {len(ods_files)} 个ODS文件:")
    for file in ods_files:
        print(f"  - {file}")
    print()
    
    # 转换每个文件
    success_count = 0
    total_count = len(ods_files)
    
    for ods_file in ods_files:
        ods_path = os.path.join(ods_folder, ods_file)
        
        # 生成CSV文件名
        csv_file = ods_file.replace('.ods', '.csv')
        csv_path = os.path.join(csv_folder, csv_file)
        
        # 执行转换
        if convert_ods_to_csv(ods_path, csv_path):
            success_count += 1
    
    # 输出转换结果统计
    print(f"\n转换完成!")
    print(f"成功转换: {success_count}/{total_count} 个文件")
    print(f"CSV文件保存在: {csv_folder} 文件夹中")
    
    if success_count < total_count:
        print(f"失败: {total_count - success_count} 个文件")

if __name__ == "__main__":
    main()
