import XCTest
import SwiftData
@testable import MusicTermsDictionary

final class MusicTermsDictionaryTests: XCTestCase {
    var modelContainer: ModelContainer!
    var modelContext: ModelContext!
    var searchService: SearchService!
    var dataManager: DataManager!
    
    override func setUpWithError() throws {
        // 创建内存中的模型容器用于测试
        let schema = Schema([MusicTerm.self, Favorite.self, SearchHistory.self])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
        modelContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
        modelContext = ModelContext(modelContainer)
        
        searchService = SearchService(modelContext: modelContext)
        dataManager = DataManager(modelContext: modelContext)
    }
    
    override func tearDownWithError() throws {
        modelContainer = nil
        modelContext = nil
        searchService = nil
        dataManager = nil
    }
    
    // MARK: - MusicTerm Tests
    
    func testMusicTermCreation() throws {
        let term = MusicTerm(
            term: "Dolce",
            meaning: "甜美的，柔和的",
            pinyin: "tianmei",
            category: "表情术语"
        )
        
        XCTAssertEqual(term.term, "Dolce")
        XCTAssertEqual(term.meaning, "甜美的，柔和的")
        XCTAssertEqual(term.pinyin, "tianmei")
        XCTAssertEqual(term.category, "表情术语")
        XCTAssertTrue(term.isEnglishTerm)
        XCTAssertFalse(term.isChineseTerm)
        XCTAssertEqual(term.firstLetter, "D")
    }
    
    func testMusicTermMatching() throws {
        let term = MusicTerm(
            term: "Dolce",
            meaning: "甜美的，柔和的",
            pinyin: "tianmei",
            category: "表情术语"
        )
        
        XCTAssertTrue(term.matches(searchText: "dolce"))
        XCTAssertTrue(term.matches(searchText: "甜美"))
        XCTAssertTrue(term.matches(searchText: "tianmei"))
        XCTAssertFalse(term.matches(searchText: "快速"))
    }
    
    func testMusicTermMatchScore() throws {
        let term = MusicTerm(
            term: "Dolce",
            meaning: "甜美的，柔和的",
            pinyin: "tianmei",
            category: "表情术语"
        )
        
        // 精确匹配应该得到最高分
        let exactScore = term.matchScore(for: "Dolce")
        XCTAssertEqual(exactScore, 1.0, accuracy: 0.01)
        
        // 前缀匹配应该得到较高分
        let prefixScore = term.matchScore(for: "Dol")
        XCTAssertGreaterThan(prefixScore, 0.7)
        
        // 拼音匹配应该得到中等分数
        let pinyinScore = term.matchScore(for: "tianmei")
        XCTAssertGreaterThan(pinyinScore, 0.8)
        
        // 不匹配应该得到0分
        let noMatchScore = term.matchScore(for: "xyz")
        XCTAssertEqual(noMatchScore, 0.0, accuracy: 0.01)
    }
    
    // MARK: - SearchService Tests
    
    func testSearchServiceExactMatch() throws {
        let terms = [
            MusicTerm(term: "Dolce", meaning: "甜美的", pinyin: "tianmei", category: "表情术语"),
            MusicTerm(term: "Forte", meaning: "强", pinyin: "qiang", category: "力度术语"),
            MusicTerm(term: "Piano", meaning: "弱", pinyin: "ruo", category: "力度术语")
        ]
        
        let results = searchService.search("Dolce", in: terms)
        
        XCTAssertEqual(results.count, 1)
        XCTAssertEqual(results.first?.term.term, "Dolce")
        XCTAssertEqual(results.first?.matchType, .exact)
        XCTAssertEqual(results.first?.score, 1.0, accuracy: 0.01)
    }
    
    func testSearchServicePrefixMatch() throws {
        let terms = [
            MusicTerm(term: "Dolce", meaning: "甜美的", pinyin: "tianmei", category: "表情术语"),
            MusicTerm(term: "Dolcissimo", meaning: "非常甜美的", pinyin: "feichang", category: "表情术语")
        ]
        
        let results = searchService.search("Dol", in: terms)
        
        XCTAssertEqual(results.count, 2)
        XCTAssertTrue(results.allSatisfy { $0.matchType == .prefix })
        XCTAssertTrue(results.allSatisfy { $0.score > 0.7 })
    }
    
    func testSearchServicePinyinMatch() throws {
        let terms = [
            MusicTerm(term: "甜美", meaning: "甜美的", pinyin: "tianmei", category: "表情术语"),
            MusicTerm(term: "强", meaning: "强的", pinyin: "qiang", category: "力度术语")
        ]
        
        let results = searchService.search("tianmei", in: terms)
        
        XCTAssertEqual(results.count, 1)
        XCTAssertEqual(results.first?.term.term, "甜美")
        XCTAssertEqual(results.first?.matchType, .pinyin)
    }
    
    func testSearchServiceEmptyQuery() throws {
        let terms = [
            MusicTerm(term: "Dolce", meaning: "甜美的", pinyin: "tianmei", category: "表情术语")
        ]
        
        let results = searchService.search("", in: terms)
        XCTAssertEqual(results.count, 0)
        
        let whitespaceResults = searchService.search("   ", in: terms)
        XCTAssertEqual(whitespaceResults.count, 0)
    }
    
    func testSearchServiceGroupTermsByLetter() throws {
        let terms = [
            MusicTerm(term: "Allegro", meaning: "快板", pinyin: "kuaiban", category: "速度术语"),
            MusicTerm(term: "Andante", meaning: "行板", pinyin: "xingban", category: "速度术语"),
            MusicTerm(term: "Dolce", meaning: "甜美的", pinyin: "tianmei", category: "表情术语"),
            MusicTerm(term: "Forte", meaning: "强", pinyin: "qiang", category: "力度术语")
        ]
        
        let grouped = searchService.groupTermsByLetter(terms)
        
        XCTAssertEqual(grouped.count, 3) // A, D, F
        XCTAssertEqual(grouped[0].0, "A")
        XCTAssertEqual(grouped[0].1.count, 2) // Allegro, Andante
        XCTAssertEqual(grouped[1].0, "D")
        XCTAssertEqual(grouped[1].1.count, 1) // Dolce
        XCTAssertEqual(grouped[2].0, "F")
        XCTAssertEqual(grouped[2].1.count, 1) // Forte
    }
    
    // MARK: - Favorite Tests
    
    func testFavoriteCreation() throws {
        let term = MusicTerm(
            term: "Dolce",
            meaning: "甜美的，柔和的",
            pinyin: "tianmei",
            category: "表情术语"
        )
        
        let favorite = Favorite.from(musicTerm: term, notes: "测试笔记")
        
        XCTAssertEqual(favorite.term, "Dolce")
        XCTAssertEqual(favorite.meaning, "甜美的，柔和的")
        XCTAssertEqual(favorite.notes, "测试笔记")
        XCTAssertEqual(favorite.termId, term.id)
    }
    
    func testFavoriteMatches() throws {
        let favorite = Favorite(
            termId: UUID(),
            term: "Dolce",
            meaning: "甜美的，柔和的",
            notes: "我的笔记"
        )
        
        XCTAssertTrue(favorite.matches(searchText: "dolce"))
        XCTAssertTrue(favorite.matches(searchText: "甜美"))
        XCTAssertTrue(favorite.matches(searchText: "笔记"))
        XCTAssertFalse(favorite.matches(searchText: "不存在"))
    }
    
    // MARK: - SearchHistory Tests
    
    func testSearchHistoryCreation() throws {
        let history = SearchHistory(
            searchText: "dolce",
            resultCount: 5,
            searchType: .general
        )
        
        XCTAssertEqual(history.searchText, "dolce")
        XCTAssertEqual(history.resultCount, 5)
        XCTAssertEqual(history.searchType, .general)
    }
    
    func testSearchHistoryGrouping() throws {
        let now = Date()
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: now)!
        let weekAgo = Calendar.current.date(byAdding: .day, value: -7, to: now)!
        
        let histories = [
            SearchHistory(searchText: "today1", resultCount: 1, searchType: .general),
            SearchHistory(searchText: "today2", resultCount: 2, searchType: .general),
            SearchHistory(searchText: "yesterday", resultCount: 3, searchType: .general),
            SearchHistory(searchText: "weekago", resultCount: 4, searchType: .general)
        ]
        
        // 手动设置日期（在实际应用中，这些会在创建时自动设置）
        histories[0].searchedAt = now
        histories[1].searchedAt = now
        histories[2].searchedAt = yesterday
        histories[3].searchedAt = weekAgo
        
        let grouped = histories.groupedByDate()
        
        XCTAssertGreaterThanOrEqual(grouped.count, 2)
        
        // 检查今天的搜索记录
        let todayGroup = grouped.first { $0.0 == "今天" }
        XCTAssertNotNil(todayGroup)
        XCTAssertEqual(todayGroup?.1.count, 2)
    }
    
    func testSearchHistoryPopularSearches() throws {
        let histories = [
            SearchHistory(searchText: "dolce", resultCount: 1, searchType: .general),
            SearchHistory(searchText: "dolce", resultCount: 2, searchType: .general),
            SearchHistory(searchText: "forte", resultCount: 3, searchType: .general),
            SearchHistory(searchText: "piano", resultCount: 4, searchType: .general),
            SearchHistory(searchText: "piano", resultCount: 5, searchType: .general),
            SearchHistory(searchText: "piano", resultCount: 6, searchType: .general)
        ]
        
        let popular = histories.popularSearches(limit: 3)
        
        XCTAssertEqual(popular.count, 3)
        XCTAssertEqual(popular[0].0, "piano") // 3次
        XCTAssertEqual(popular[0].1, 3)
        XCTAssertEqual(popular[1].0, "dolce") // 2次
        XCTAssertEqual(popular[1].1, 2)
        XCTAssertEqual(popular[2].0, "forte") // 1次
        XCTAssertEqual(popular[2].1, 1)
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformance() throws {
        // 创建大量测试数据
        var terms: [MusicTerm] = []
        for i in 0..<1000 {
            terms.append(MusicTerm(
                term: "Term\(i)",
                meaning: "Meaning\(i)",
                pinyin: "pinyin\(i)",
                category: "Category\(i % 10)"
            ))
        }
        
        measure {
            let _ = searchService.search("Term", in: terms)
        }
    }
}
