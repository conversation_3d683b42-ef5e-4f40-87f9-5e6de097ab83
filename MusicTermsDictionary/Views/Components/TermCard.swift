import SwiftUI
import SwiftData

// 基础术语卡片
struct TermCard: View {
    let term: MusicTerm
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    // 术语名称
                    Text(term.term)
                        .font(.system(.headline, design: .monospaced, weight: .semibold))
                        .foregroundColor(.black)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    // 类型指示器
                    if term.isEnglishTerm {
                        Text("EN")
                            .font(.system(.caption2, design: .monospaced, weight: .bold))
                            .foregroundColor(.black.opacity(0.6))
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .stroke(Color.black.opacity(0.3), lineWidth: 1)
                            )
                    } else if term.isChineseTerm {
                        Text("中")
                            .font(.system(.caption2, design: .monospaced, weight: .bold))
                            .foregroundColor(.black.opacity(0.6))
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .stroke(Color.black.opacity(0.3), lineWidth: 1)
                            )
                    }
                }
                
                // 释义
                Text(term.meaning)
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.8))
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                
                // 拼音（如果有）
                if !term.pinyin.isEmpty {
                    Text(term.pinyin)
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.5))
                        .italic()
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 搜索结果卡片
struct SearchResultCard: View {
    let result: SearchResult
    let searchText: String
    let index: Int
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    // 序号
                    Text("\(index)")
                        .font(.system(.caption, design: .monospaced, weight: .bold))
                        .foregroundColor(.black.opacity(0.4))
                        .frame(width: 20, alignment: .leading)
                    
                    VStack(alignment: .leading, spacing: 6) {
                        HStack {
                            // 高亮显示的术语名称
                            HighlightedText(
                                text: result.term.term,
                                highlight: searchText,
                                font: .system(.headline, design: .monospaced, weight: .semibold),
                                highlightColor: .black,
                                normalColor: .black.opacity(0.7)
                            )
                            
                            Spacer()
                            
                            // 匹配度和类型
                            HStack(spacing: 4) {
                                Text("\(Int(result.score * 100))%")
                                    .font(.system(.caption2, design: .monospaced, weight: .bold))
                                    .foregroundColor(.black.opacity(0.6))
                                
                                Text(result.matchType.displayName)
                                    .font(.system(.caption2, design: .monospaced))
                                    .foregroundColor(.black.opacity(0.4))
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .stroke(Color.black.opacity(0.2), lineWidth: 1)
                            )
                        }
                        
                        // 高亮显示的释义
                        HighlightedText(
                            text: result.term.meaning,
                            highlight: searchText,
                            font: .system(.body, design: .monospaced),
                            highlightColor: .black,
                            normalColor: .black.opacity(0.8)
                        )
                        .lineLimit(2)
                        
                        // 拼音（如果匹配）
                        if !result.term.pinyin.isEmpty && result.matchType == .pinyin {
                            HighlightedText(
                                text: result.term.pinyin,
                                highlight: searchText,
                                font: .system(.caption, design: .monospaced),
                                highlightColor: .black.opacity(0.7),
                                normalColor: .black.opacity(0.5)
                            )
                            .italic()
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .overlay(
                Rectangle()
                    .fill(Color.black.opacity(0.1))
                    .frame(height: 1),
                alignment: .bottom
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 高亮文本组件
struct HighlightedText: View {
    let text: String
    let highlight: String
    let font: Font
    let highlightColor: Color
    let normalColor: Color
    
    var body: some View {
        let attributedString = createAttributedString()
        
        Text(AttributedString(attributedString))
            .font(font)
    }
    
    private func createAttributedString() -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: text)
        let range = NSRange(location: 0, length: text.count)
        
        // 设置默认颜色
        attributedString.addAttribute(.foregroundColor, value: UIColor(normalColor), range: range)
        
        // 查找并高亮匹配的文本
        let lowercaseText = text.lowercased()
        let lowercaseHighlight = highlight.lowercased()
        
        var searchRange = NSRange(location: 0, length: lowercaseText.count)
        
        while searchRange.location < lowercaseText.count {
            let foundRange = (lowercaseText as NSString).range(of: lowercaseHighlight, options: [], range: searchRange)
            
            if foundRange.location == NSNotFound {
                break
            }
            
            // 高亮找到的文本
            attributedString.addAttribute(.foregroundColor, value: UIColor(highlightColor), range: foundRange)
            attributedString.addAttribute(.font, value: UIFont.monospacedSystemFont(ofSize: 16, weight: .bold), range: foundRange)
            
            // 更新搜索范围
            searchRange.location = foundRange.location + foundRange.length
            searchRange.length = lowercaseText.count - searchRange.location
        }
        
        return attributedString
    }
}

// 术语详情视图
struct TermDetailView: View {
    let term: MusicTerm
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @State private var isFavorited = false
    @State private var showingFavoriteAnimation = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 术语标题
                    VStack(alignment: .leading, spacing: 8) {
                        Text(term.term)
                            .font(.system(.largeTitle, design: .monospaced, weight: .bold))
                            .foregroundColor(.black)
                        
                        if !term.pinyin.isEmpty {
                            Text(term.pinyin)
                                .font(.system(.title3, design: .monospaced))
                                .foregroundColor(.black.opacity(0.6))
                                .italic()
                        }
                    }
                    
                    // 分割线
                    Rectangle()
                        .fill(Color.black)
                        .frame(height: 2)
                    
                    // 释义
                    VStack(alignment: .leading, spacing: 8) {
                        Text("释义")
                            .font(.system(.headline, design: .monospaced, weight: .semibold))
                            .foregroundColor(.black)
                        
                        Text(term.meaning)
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(.black.opacity(0.8))
                            .lineSpacing(4)
                    }
                    
                    // 分类信息
                    if !term.category.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("分类")
                                .font(.system(.headline, design: .monospaced, weight: .semibold))
                                .foregroundColor(.black)
                            
                            Text(term.category)
                                .font(.system(.body, design: .monospaced))
                                .foregroundColor(.black.opacity(0.8))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 0)
                                        .stroke(Color.black.opacity(0.3), lineWidth: 1)
                                )
                        }
                    }
                    
                    Spacer()
                }
                .padding(24)
            }
            .navigationTitle("术语详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: toggleFavorite) {
                        Image(systemName: isFavorited ? "heart.fill" : "heart")
                            .font(.system(size: 18))
                            .foregroundColor(.black)
                            .scaleEffect(showingFavoriteAnimation ? 1.3 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: showingFavoriteAnimation)
                    }
                }
            }
            .background(Color.white)
        }
        .onAppear {
            checkIfFavorited()
        }
    }
    
    private func checkIfFavorited() {
        let descriptor = FetchDescriptor<Favorite>(
            predicate: #Predicate<Favorite> { $0.termId == term.id }
        )
        
        do {
            let favorites = try modelContext.fetch(descriptor)
            isFavorited = !favorites.isEmpty
        } catch {
            print("检查收藏状态失败: \(error)")
        }
    }
    
    private func toggleFavorite() {
        if isFavorited {
            removeFavorite()
        } else {
            addFavorite()
        }
        
        showingFavoriteAnimation = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            showingFavoriteAnimation = false
        }
    }
    
    private func addFavorite() {
        let favorite = Favorite.from(musicTerm: term)
        modelContext.insert(favorite)
        
        do {
            try modelContext.save()
            isFavorited = true
        } catch {
            print("添加收藏失败: \(error)")
        }
    }
    
    private func removeFavorite() {
        let descriptor = FetchDescriptor<Favorite>(
            predicate: #Predicate<Favorite> { $0.termId == term.id }
        )
        
        do {
            let favorites = try modelContext.fetch(descriptor)
            for favorite in favorites {
                modelContext.delete(favorite)
            }
            try modelContext.save()
            isFavorited = false
        } catch {
            print("移除收藏失败: \(error)")
        }
    }
}

#Preview {
    let sampleTerm = MusicTerm(
        term: "Dolce",
        meaning: "甜美的，柔和的",
        pinyin: "tianmei",
        category: "表情术语"
    )
    
    VStack {
        TermCard(term: sampleTerm) {}
        
        SearchResultCard(
            result: SearchResult(
                term: sampleTerm,
                score: 0.95,
                matchType: .exact
            ),
            searchText: "dolce",
            index: 1
        ) {}
    }
    .padding()
    .background(Color.white)
}
