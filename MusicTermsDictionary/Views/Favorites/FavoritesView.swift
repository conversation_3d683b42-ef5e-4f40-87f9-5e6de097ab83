import SwiftUI
import SwiftData

struct FavoritesView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \Favorite.createdAt, order: .reverse) private var favorites: [Favorite]
    
    @State private var searchText = ""
    @State private var selectedFavorite: Favorite?
    @State private var showingDeleteAlert = false
    @State private var favoriteToDelete: Favorite?
    @State private var showingClearAllAlert = false
    
    private var filteredFavorites: [Favorite] {
        if searchText.isEmpty {
            return Array(favorites)
        } else {
            return favorites.filter { $0.matches(searchText: searchText) }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if favorites.isEmpty {
                    // 空状态
                    EmptyFavoritesView()
                } else {
                    // 搜索栏
                    if favorites.count > 5 {
                        FavoriteSearchBar(text: $searchText)
                            .padding(.horizontal, 16)
                            .padding(.top, 8)
                        
                        Rectangle()
                            .fill(Color.black)
                            .frame(height: 1)
                            .padding(.horizontal, 16)
                            .padding(.top, 8)
                    }
                    
                    // 收藏列表
                    if filteredFavorites.isEmpty {
                        SearchEmptyView(searchText: searchText)
                    } else {
                        FavoritesList(
                            favorites: filteredFavorites,
                            onFavoriteSelected: { favorite in
                                selectedFavorite = favorite
                            },
                            onDeleteFavorite: { favorite in
                                favoriteToDelete = favorite
                                showingDeleteAlert = true
                            }
                        )
                    }
                }
            }
            .navigationTitle("收藏夹")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                if !favorites.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Menu {
                            Button(action: {
                                showingClearAllAlert = true
                            }) {
                                Label("清空收藏夹", systemImage: "trash")
                            }
                            
                            Button(action: exportFavorites) {
                                Label("导出收藏", systemImage: "square.and.arrow.up")
                            }
                        } label: {
                            Image(systemName: "ellipsis.circle")
                                .font(.system(size: 18))
                                .foregroundColor(.black)
                        }
                    }
                }
            }
            .background(Color.white)
        }
        .sheet(item: $selectedFavorite) { favorite in
            FavoriteDetailView(favorite: favorite)
        }
        .alert("删除收藏", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                if let favorite = favoriteToDelete {
                    deleteFavorite(favorite)
                }
            }
        } message: {
            Text("确定要删除这个收藏吗？")
        }
        .alert("清空收藏夹", isPresented: $showingClearAllAlert) {
            Button("取消", role: .cancel) {}
            Button("清空", role: .destructive) {
                clearAllFavorites()
            }
        } message: {
            Text("确定要清空所有收藏吗？此操作无法撤销。")
        }
        .preferredColorScheme(.light)
    }
    
    private func deleteFavorite(_ favorite: Favorite) {
        modelContext.delete(favorite)
        
        do {
            try modelContext.save()
        } catch {
            print("删除收藏失败: \(error)")
        }
    }
    
    private func clearAllFavorites() {
        for favorite in favorites {
            modelContext.delete(favorite)
        }
        
        do {
            try modelContext.save()
        } catch {
            print("清空收藏夹失败: \(error)")
        }
    }
    
    private func exportFavorites() {
        // TODO: 实现导出功能
        print("导出收藏功能待实现")
    }
}

// 空状态视图
struct EmptyFavoritesView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "heart")
                .font(.system(size: 64, weight: .thin))
                .foregroundColor(.black.opacity(0.3))
            
            VStack(spacing: 8) {
                Text("暂无收藏")
                    .font(.system(.title2, design: .monospaced, weight: .semibold))
                    .foregroundColor(.black)
                
                Text("在主页搜索术语并收藏")
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("收藏小贴士：")
                    .font(.system(.caption, design: .monospaced, weight: .semibold))
                    .foregroundColor(.black.opacity(0.8))
                
                Text("• 点击术语查看详情")
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
                
                Text("• 点击心形图标收藏")
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
                
                Text("• 收藏的术语会显示在这里")
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
            }
            .padding(.top, 16)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

// 收藏搜索栏
struct FavoriteSearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16))
                .foregroundColor(.black.opacity(0.6))
            
            TextField("搜索收藏的术语...", text: $text)
                .font(.system(.body, design: .monospaced))
                .foregroundColor(.black)
            
            if !text.isEmpty {
                Button(action: {
                    text = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.black.opacity(0.4))
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 0)
                .stroke(Color.black, lineWidth: 1)
                .background(Color.white)
        )
    }
}

// 搜索空状态
struct SearchEmptyView: View {
    let searchText: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48, weight: .thin))
                .foregroundColor(.black.opacity(0.3))
            
            Text("未找到匹配的收藏")
                .font(.system(.title3, design: .monospaced))
                .foregroundColor(.black)
            
            Text("尝试使用不同的关键词")
                .font(.system(.body, design: .monospaced))
                .foregroundColor(.black.opacity(0.6))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

// 收藏列表
struct FavoritesList: View {
    let favorites: [Favorite]
    let onFavoriteSelected: (Favorite) -> Void
    let onDeleteFavorite: (Favorite) -> Void
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(favorites, id: \.id) { favorite in
                    FavoriteCard(
                        favorite: favorite,
                        onTap: {
                            onFavoriteSelected(favorite)
                        },
                        onDelete: {
                            onDeleteFavorite(favorite)
                        }
                    )
                    
                    if favorite.id != favorites.last?.id {
                        Rectangle()
                            .fill(Color.black.opacity(0.1))
                            .frame(height: 1)
                            .padding(.leading, 16)
                    }
                }
            }
            .padding(.top, 16)
        }
    }
}

// 收藏卡片
struct FavoriteCard: View {
    let favorite: Favorite
    let onTap: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Button(action: onTap) {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(favorite.term)
                            .font(.system(.headline, design: .monospaced, weight: .semibold))
                            .foregroundColor(.black)
                        
                        Spacer()
                        
                        Text(favorite.createdAt.formatted(date: .abbreviated, time: .omitted))
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.black.opacity(0.5))
                    }
                    
                    Text(favorite.meaning)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.black.opacity(0.8))
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                    
                    if !favorite.notes.isEmpty {
                        Text("笔记: \(favorite.notes)")
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.black.opacity(0.6))
                            .italic()
                            .lineLimit(1)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .buttonStyle(PlainButtonStyle())
            
            Button(action: onDelete) {
                Image(systemName: "heart.fill")
                    .font(.system(size: 18))
                    .foregroundColor(.black.opacity(0.6))
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.white)
    }
}

// 收藏详情视图
struct FavoriteDetailView: View {
    @Bindable var favorite: Favorite
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @State private var editingNotes = false
    @State private var notesText = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 术语信息
                    VStack(alignment: .leading, spacing: 8) {
                        Text(favorite.term)
                            .font(.system(.largeTitle, design: .monospaced, weight: .bold))
                            .foregroundColor(.black)
                        
                        Text(favorite.meaning)
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(.black.opacity(0.8))
                            .lineSpacing(4)
                    }
                    
                    Rectangle()
                        .fill(Color.black)
                        .frame(height: 2)
                    
                    // 收藏信息
                    VStack(alignment: .leading, spacing: 8) {
                        Text("收藏时间")
                            .font(.system(.headline, design: .monospaced, weight: .semibold))
                            .foregroundColor(.black)
                        
                        Text(favorite.createdAt.formatted(date: .complete, time: .shortened))
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(.black.opacity(0.8))
                    }
                    
                    // 笔记
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("笔记")
                                .font(.system(.headline, design: .monospaced, weight: .semibold))
                                .foregroundColor(.black)
                            
                            Spacer()
                            
                            Button(editingNotes ? "保存" : "编辑") {
                                if editingNotes {
                                    saveNotes()
                                } else {
                                    startEditingNotes()
                                }
                            }
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(.black)
                        }
                        
                        if editingNotes {
                            TextField("添加你的笔记...", text: $notesText, axis: .vertical)
                                .font(.system(.body, design: .monospaced))
                                .foregroundColor(.black)
                                .padding(12)
                                .background(
                                    RoundedRectangle(cornerRadius: 0)
                                        .stroke(Color.black, lineWidth: 1)
                                        .background(Color.white)
                                )
                                .lineLimit(5...10)
                        } else {
                            if favorite.notes.isEmpty {
                                Text("暂无笔记")
                                    .font(.system(.body, design: .monospaced))
                                    .foregroundColor(.black.opacity(0.5))
                                    .italic()
                            } else {
                                Text(favorite.notes)
                                    .font(.system(.body, design: .monospaced))
                                    .foregroundColor(.black.opacity(0.8))
                                    .lineSpacing(4)
                            }
                        }
                    }
                    
                    Spacer()
                }
                .padding(24)
            }
            .navigationTitle("收藏详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black)
                }
                
                if editingNotes {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("取消") {
                            cancelEditingNotes()
                        }
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.black)
                    }
                }
            }
            .background(Color.white)
        }
        .onAppear {
            notesText = favorite.notes
        }
    }
    
    private func startEditingNotes() {
        editingNotes = true
        notesText = favorite.notes
    }
    
    private func saveNotes() {
        favorite.updateNotes(notesText)
        
        do {
            try modelContext.save()
            editingNotes = false
        } catch {
            print("保存笔记失败: \(error)")
        }
    }
    
    private func cancelEditingNotes() {
        editingNotes = false
        notesText = favorite.notes
    }
}

#Preview {
    FavoritesView()
        .modelContainer(for: [MusicTerm.self, Favorite.self, SearchHistory.self])
}
