//
//  HistoryView.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import SwiftUI
import SwiftData

struct HistoryView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \SearchHistory.searchedAt, order: .reverse) private var searchHistory: [SearchHistory]
    
    @State private var selectedSegment = 0
    @State private var showingClearAlert = false
    @State private var practiceMode = false
    @State private var currentPracticeIndex = 0
    @State private var practiceTerms: [MusicTerm] = []
    
    private let segments = ["历史记录", "练习模式"]
    
    private var groupedHistory: [(String, [SearchHistory])] {
        searchHistory.groupedByDate()
    }
    
    private var popularSearches: [(String, Int)] {
        searchHistory.popularSearches(limit: 5)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 分段控制器
                Picker("模式", selection: $selectedSegment) {
                    ForEach(0..<segments.count, id: \.self) { index in
                        Text(segments[index])
                            .font(.system(.body, design: .monospaced))
                            .tag(index)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal, 16)
                .padding(.top, 8)
                
                Rectangle()
                    .fill(Color.black)
                    .frame(height: 1)
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                
                // 内容区域
                if selectedSegment == 0 {
                    // 历史记录
                    if searchHistory.isEmpty {
                        EmptyHistoryView()
                    } else {
                        HistoryListView(
                            groupedHistory: groupedHistory,
                            popularSearches: popularSearches,
                            onSearchAgain: { searchText in
                                // TODO: 实现重新搜索功能
                                print("重新搜索: \(searchText)")
                            }
                        )
                    }
                } else {
                    // 练习模式
                    if practiceMode {
                        PracticeModeView(
                            terms: practiceTerms,
                            currentIndex: $currentPracticeIndex,
                            onExit: {
                                practiceMode = false
                                currentPracticeIndex = 0
                            }
                        )
                    } else {
                        PracticeSetupView(
                            onStartPractice: { terms in
                                practiceTerms = terms
                                practiceMode = true
                                currentPracticeIndex = 0
                            }
                        )
                    }
                }
            }
            .navigationTitle("历史记录")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                if selectedSegment == 0 && !searchHistory.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("清空") {
                            showingClearAlert = true
                        }
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.black)
                    }
                }
            }
            .background(Color.white)
        }
        .alert("清空历史记录", isPresented: $showingClearAlert) {
            Button("取消", role: .cancel) {}
            Button("清空", role: .destructive) {
                clearHistory()
            }
        } message: {
            Text("确定要清空所有搜索历史吗？")
        }
        .preferredColorScheme(.light)
    }
    
    private func clearHistory() {
        for history in searchHistory {
            modelContext.delete(history)
        }
        
        do {
            try modelContext.save()
        } catch {
            print("清空历史记录失败: \(error)")
        }
    }
}

// 空历史状态
struct EmptyHistoryView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "clock")
                .font(.system(size: 64, weight: .thin))
                .foregroundColor(.black.opacity(0.3))
            
            VStack(spacing: 8) {
                Text("暂无搜索历史")
                    .font(.system(.title2, design: .monospaced, weight: .semibold))
                    .foregroundColor(.black)
                
                Text("开始搜索音乐术语")
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

// 历史记录列表
struct HistoryListView: View {
    let groupedHistory: [(String, [SearchHistory])]
    let popularSearches: [(String, Int)]
    let onSearchAgain: (String) -> Void
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
                // 热门搜索
                if !popularSearches.isEmpty {
                    Section {
                        VStack(spacing: 8) {
                            ForEach(popularSearches, id: \.0) { search, count in
                                PopularSearchCard(
                                    searchText: search,
                                    count: count,
                                    onTap: {
                                        onSearchAgain(search)
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.bottom, 16)
                    } header: {
                        SectionHeader(title: "热门搜索")
                    }
                }
                
                // 历史记录分组
                ForEach(groupedHistory, id: \.0) { dateGroup, histories in
                    Section {
                        ForEach(histories, id: \.id) { history in
                            HistoryCard(
                                history: history,
                                onSearchAgain: {
                                    onSearchAgain(history.searchText)
                                }
                            )
                            
                            if history.id != histories.last?.id {
                                Rectangle()
                                    .fill(Color.black.opacity(0.1))
                                    .frame(height: 1)
                                    .padding(.leading, 16)
                            }
                        }
                    } header: {
                        SectionHeader(title: dateGroup)
                    }
                }
            }
            .padding(.top, 16)
        }
    }
}

// 热门搜索卡片
struct PopularSearchCard: View {
    let searchText: String
    let count: Int
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(searchText)
                        .font(.system(.body, design: .monospaced, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Text("搜索 \(count) 次")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                }
                
                Spacer()
                
                Image(systemName: "arrow.up.right")
                    .font(.system(size: 14))
                    .foregroundColor(.black.opacity(0.4))
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 0)
                    .stroke(Color.black.opacity(0.2), lineWidth: 1)
                    .background(Color.white)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 历史记录卡片
struct HistoryCard: View {
    let history: SearchHistory
    let onSearchAgain: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 搜索类型图标
            Image(systemName: history.searchType.icon)
                .font(.system(size: 16))
                .foregroundColor(.black.opacity(0.6))
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(history.searchText)
                    .font(.system(.body, design: .monospaced, weight: .medium))
                    .foregroundColor(.black)
                
                HStack {
                    Text(history.resultDescription)
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                    
                    Text("•")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.4))
                    
                    Text(history.relativeTime)
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                }
            }
            
            Spacer()
            
            Button(action: onSearchAgain) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 14))
                    .foregroundColor(.black.opacity(0.6))
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.white)
    }
}

// 练习设置视图
struct PracticeSetupView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var selectedCount = 10
    @State private var selectedDifficulty = 0
    
    private let countOptions = [5, 10, 20, 50]
    private let difficultyOptions = ["随机", "基础", "进阶", "高级"]
    
    let onStartPractice: ([MusicTerm]) -> Void
    
    var body: some View {
        VStack(spacing: 32) {
            VStack(spacing: 16) {
                Image(systemName: "brain.head.profile")
                    .font(.system(size: 64, weight: .thin))
                    .foregroundColor(.black.opacity(0.6))
                
                Text("练习模式")
                    .font(.system(.title, design: .monospaced, weight: .bold))
                    .foregroundColor(.black)
                
                Text("通过练习加深对音乐术语的理解")
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.black.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 24) {
                // 题目数量选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("题目数量")
                        .font(.system(.headline, design: .monospaced, weight: .semibold))
                        .foregroundColor(.black)
                    
                    HStack(spacing: 8) {
                        ForEach(countOptions, id: \.self) { count in
                            Button("\(count)") {
                                selectedCount = count
                            }
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(selectedCount == count ? .white : .black)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .fill(selectedCount == count ? Color.black : Color.white)
                                    .stroke(Color.black, lineWidth: 1)
                            )
                        }
                    }
                }
                
                // 难度选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("难度等级")
                        .font(.system(.headline, design: .monospaced, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Picker("难度", selection: $selectedDifficulty) {
                        ForEach(0..<difficultyOptions.count, id: \.self) { index in
                            Text(difficultyOptions[index])
                                .font(.system(.body, design: .monospaced))
                                .tag(index)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
            }
            
            Button("开始练习") {
                startPractice()
            }
            .font(.system(.headline, design: .monospaced, weight: .semibold))
            .foregroundColor(.white)
            .padding(.horizontal, 32)
            .padding(.vertical, 12)
            .background(Color.black)
            .disabled(selectedCount == 0)
            
            Spacer()
        }
        .padding(32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
    
    private func startPractice() {
        // 获取术语数据
        let descriptor = FetchDescriptor<MusicTerm>()
        
        do {
            let allTerms = try modelContext.fetch(descriptor)
            let practiceTerms = Array(allTerms.shuffled().prefix(selectedCount))
            onStartPractice(practiceTerms)
        } catch {
            print("获取练习术语失败: \(error)")
        }
    }
}

// 练习模式视图
struct PracticeModeView: View {
    let terms: [MusicTerm]
    @Binding var currentIndex: Int
    let onExit: () -> Void
    
    @State private var showingAnswer = false
    @State private var userAnswer = ""
    @State private var score = 0
    @State private var showingResult = false
    
    private var currentTerm: MusicTerm? {
        guard currentIndex < terms.count else { return nil }
        return terms[currentIndex]
    }
    
    private var progress: Double {
        guard !terms.isEmpty else { return 0 }
        return Double(currentIndex) / Double(terms.count)
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 进度条
            VStack(spacing: 8) {
                HStack {
                    Text("进度")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.black.opacity(0.6))
                    
                    Spacer()
                    
                    Text("\(currentIndex + 1) / \(terms.count)")
                        .font(.system(.caption, design: .monospaced, weight: .semibold))
                        .foregroundColor(.black)
                }
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .black))
                    .background(Color.black.opacity(0.1))
            }
            
            if let term = currentTerm {
                // 题目内容
                VStack(spacing: 16) {
                    Text("这个术语的含义是什么？")
                        .font(.system(.headline, design: .monospaced))
                        .foregroundColor(.black.opacity(0.8))
                    
                    Text(term.term)
                        .font(.system(.largeTitle, design: .monospaced, weight: .bold))
                        .foregroundColor(.black)
                        .padding(.vertical, 16)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 0)
                                .stroke(Color.black, lineWidth: 2)
                                .background(Color.white)
                        )
                    
                    if showingAnswer {
                        VStack(spacing: 8) {
                            Text("正确答案：")
                                .font(.system(.body, design: .monospaced, weight: .semibold))
                                .foregroundColor(.black)
                            
                            Text(term.meaning)
                                .font(.system(.body, design: .monospaced))
                                .foregroundColor(.black.opacity(0.8))
                                .multilineTextAlignment(.center)
                                .lineSpacing(4)
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 0)
                                .fill(Color.black.opacity(0.05))
                                .stroke(Color.black.opacity(0.2), lineWidth: 1)
                        )
                    }
                }
                
                // 操作按钮
                VStack(spacing: 12) {
                    if !showingAnswer {
                        Button("显示答案") {
                            showingAnswer = true
                        }
                        .font(.system(.body, design: .monospaced, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 10)
                        .background(Color.black)
                    } else {
                        HStack(spacing: 16) {
                            Button("答错了") {
                                nextQuestion()
                            }
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(.black)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 0)
                                    .stroke(Color.black, lineWidth: 1)
                                    .background(Color.white)
                            )
                            
                            Button("答对了") {
                                score += 1
                                nextQuestion()
                            }
                            .font(.system(.body, design: .monospaced, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(Color.black)
                        }
                    }
                }
            }
            
            Spacer()
            
            // 退出按钮
            Button("退出练习") {
                onExit()
            }
            .font(.system(.body, design: .monospaced))
            .foregroundColor(.black.opacity(0.6))
        }
        .padding(24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
        .sheet(isPresented: $showingResult) {
            PracticeResultView(
                score: score,
                total: terms.count,
                onRestart: {
                    currentIndex = 0
                    score = 0
                    showingResult = false
                },
                onExit: onExit
            )
        }
    }
    
    private func nextQuestion() {
        showingAnswer = false
        currentIndex += 1
        
        if currentIndex >= terms.count {
            showingResult = true
        }
    }
}

// 练习结果视图
struct PracticeResultView: View {
    let score: Int
    let total: Int
    let onRestart: () -> Void
    let onExit: () -> Void
    
    private var percentage: Int {
        guard total > 0 else { return 0 }
        return Int(Double(score) / Double(total) * 100)
    }
    
    var body: some View {
        VStack(spacing: 32) {
            VStack(spacing: 16) {
                Image(systemName: percentage >= 80 ? "star.fill" : "checkmark.circle")
                    .font(.system(size: 64, weight: .thin))
                    .foregroundColor(.black.opacity(0.6))
                
                Text("练习完成")
                    .font(.system(.title, design: .monospaced, weight: .bold))
                    .foregroundColor(.black)
            }
            
            VStack(spacing: 16) {
                Text("得分")
                    .font(.system(.headline, design: .monospaced))
                    .foregroundColor(.black.opacity(0.8))
                
                Text("\(score) / \(total)")
                    .font(.system(.largeTitle, design: .monospaced, weight: .bold))
                    .foregroundColor(.black)
                
                Text("\(percentage)%")
                    .font(.system(.title2, design: .monospaced))
                    .foregroundColor(.black.opacity(0.6))
            }
            
            VStack(spacing: 12) {
                Button("再练一次") {
                    onRestart()
                }
                .font(.system(.headline, design: .monospaced, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 12)
                .background(Color.black)
                
                Button("退出") {
                    onExit()
                }
                .font(.system(.body, design: .monospaced))
                .foregroundColor(.black.opacity(0.6))
            }
        }
        .padding(32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

// 通用分组标题
struct SectionHeader: View {
    let title: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.system(.headline, design: .monospaced, weight: .semibold))
                .foregroundColor(.black)
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.white)
        .overlay(
            Rectangle()
                .fill(Color.black.opacity(0.2))
                .frame(height: 1),
            alignment: .bottom
        )
    }
}

#Preview {
    HistoryView()
        .modelContainer(for: [MusicTerm.self, Favorite.self, SearchHistory.self])
}
