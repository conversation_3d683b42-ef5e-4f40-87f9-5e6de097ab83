import SwiftUI

struct SearchBar: View {
    @Binding var text: String
    @Binding var isSearching: Bool
    let onSearchChanged: () -> Void
    let onClear: () -> Void
    
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // 搜索图标
            Image(systemName: "magnifyingglass")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.black.opacity(0.6))
            
            // 搜索输入框
            TextField("搜索音乐术语...", text: $text)
                .font(.system(.body, design: .monospaced))
                .foregroundColor(.black)
                .focused($isTextFieldFocused)
                .onChange(of: text) { _, newValue in
                    onSearchChanged()
                }
                .onTapGesture {
                    isSearching = true
                }
            
            // 清除按钮
            if !text.isEmpty {
                Button(action: {
                    text = ""
                    onClear()
                    isTextFieldFocused = false
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.black.opacity(0.4))
                }
                .transition(.scale.combined(with: .opacity))
            }
            
            // 取消按钮
            if isSearching {
                Button("取消") {
                    text = ""
                    onClear()
                    isTextFieldFocused = false
                }
                .font(.system(.body, design: .monospaced))
                .foregroundColor(.black)
                .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 0)
                .stroke(Color.black, lineWidth: 2)
                .background(Color.white)
        )
        .animation(.easeInOut(duration: 0.2), value: isSearching)
        .animation(.easeInOut(duration: 0.2), value: text.isEmpty)
        .onChange(of: isTextFieldFocused) { _, focused in
            if focused {
                isSearching = true
            } else if text.isEmpty {
                isSearching = false
            }
        }
    }
}

// 搜索建议视图
struct SearchSuggestions: View {
    let suggestions: [String]
    let onSuggestionTapped: (String) -> Void
    
    var body: some View {
        if !suggestions.isEmpty {
            VStack(spacing: 0) {
                ForEach(suggestions, id: \.self) { suggestion in
                    Button(action: {
                        onSuggestionTapped(suggestion)
                    }) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 14))
                                .foregroundColor(.black.opacity(0.4))
                            
                            Text(suggestion)
                                .font(.system(.body, design: .monospaced))
                                .foregroundColor(.black)
                            
                            Spacer()
                            
                            Image(systemName: "arrow.up.left")
                                .font(.system(size: 12))
                                .foregroundColor(.black.opacity(0.3))
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.white)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    if suggestion != suggestions.last {
                        Rectangle()
                            .fill(Color.black.opacity(0.1))
                            .frame(height: 1)
                            .padding(.leading, 48)
                    }
                }
            }
            .background(
                RoundedRectangle(cornerRadius: 0)
                    .stroke(Color.black, lineWidth: 1)
                    .background(Color.white)
            )
            .padding(.horizontal, 16)
            .padding(.top, 4)
        }
    }
}

#Preview {
    VStack {
        SearchBar(
            text: .constant("dolce"),
            isSearching: .constant(true),
            onSearchChanged: {},
            onClear: {}
        )
        
        SearchSuggestions(
            suggestions: ["Dolce", "Dolcissimo", "Dolente"],
            onSuggestionTapped: { _ in }
        )
        
        Spacer()
    }
    .padding()
    .background(Color.white)
}
