import Foundation
import UIKit

// 导出格式枚举
enum ExportFormat: String, CaseIterable {
    case text = "text"
    case json = "json"
    case csv = "csv"
    
    var displayName: String {
        switch self {
        case .text: return "文本格式"
        case .json: return "JSON格式"
        case .csv: return "CSV格式"
        }
    }
    
    var fileExtension: String {
        switch self {
        case .text: return "txt"
        case .json: return "json"
        case .csv: return "csv"
        }
    }
    
    var mimeType: String {
        switch self {
        case .text: return "text/plain"
        case .json: return "application/json"
        case .csv: return "text/csv"
        }
    }
}

// 导出数据类型
enum ExportDataType {
    case allTerms([MusicTerm])
    case favorites([Favorite])
    case searchHistory([SearchHistory])
    case practiceResults(score: Int, total: Int, terms: [MusicTerm])
}

@Observable
class ExportService {
    private(set) var isExporting = false
    private(set) var exportProgress: Double = 0
    private(set) var errorMessage: String?
    
    // 导出数据
    func exportData(
        _ dataType: ExportDataType,
        format: ExportFormat,
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        isExporting = true
        exportProgress = 0
        errorMessage = nil
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            do {
                let content = try self?.generateContent(dataType, format: format) ?? ""
                let url = try self?.saveToFile(content: content, format: format, dataType: dataType) ?? URL(fileURLWithPath: "")
                
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 1.0
                    completion(.success(url))
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.errorMessage = error.localizedDescription
                    completion(.failure(error))
                }
            }
        }
    }
    
    // 生成导出内容
    private func generateContent(_ dataType: ExportDataType, format: ExportFormat) throws -> String {
        switch format {
        case .text:
            return try generateTextContent(dataType)
        case .json:
            return try generateJSONContent(dataType)
        case .csv:
            return try generateCSVContent(dataType)
        }
    }
    
    // 生成文本格式内容
    private func generateTextContent(_ dataType: ExportDataType) throws -> String {
        var content = ""
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .short
        
        switch dataType {
        case .allTerms(let terms):
            content += "音乐术语词典\n"
            content += "导出时间: \(dateFormatter.string(from: Date()))\n"
            content += "术语总数: \(terms.count)\n"
            content += String(repeating: "=", count: 50) + "\n\n"
            
            for (index, term) in terms.enumerated() {
                content += "\(index + 1). \(term.term)\n"
                content += "   释义: \(term.meaning)\n"
                if !term.pinyin.isEmpty {
                    content += "   拼音: \(term.pinyin)\n"
                }
                if !term.category.isEmpty {
                    content += "   分类: \(term.category)\n"
                }
                content += "\n"
                
                // 更新进度
                DispatchQueue.main.async {
                    self.exportProgress = Double(index + 1) / Double(terms.count) * 0.8
                }
            }
            
        case .favorites(let favorites):
            content += "收藏的音乐术语\n"
            content += "导出时间: \(dateFormatter.string(from: Date()))\n"
            content += "收藏总数: \(favorites.count)\n"
            content += String(repeating: "=", count: 50) + "\n\n"
            
            for (index, favorite) in favorites.enumerated() {
                content += "\(index + 1). \(favorite.term)\n"
                content += "   释义: \(favorite.meaning)\n"
                content += "   收藏时间: \(dateFormatter.string(from: favorite.createdAt))\n"
                if !favorite.notes.isEmpty {
                    content += "   笔记: \(favorite.notes)\n"
                }
                content += "\n"
                
                DispatchQueue.main.async {
                    self.exportProgress = Double(index + 1) / Double(favorites.count) * 0.8
                }
            }
            
        case .searchHistory(let history):
            content += "搜索历史记录\n"
            content += "导出时间: \(dateFormatter.string(from: Date()))\n"
            content += "记录总数: \(history.count)\n"
            content += String(repeating: "=", count: 50) + "\n\n"
            
            for (index, record) in history.enumerated() {
                content += "\(index + 1). \(record.searchText)\n"
                content += "   搜索时间: \(dateFormatter.string(from: record.searchedAt))\n"
                content += "   结果数量: \(record.resultCount)\n"
                content += "   搜索类型: \(record.searchType.displayName)\n"
                content += "\n"
                
                DispatchQueue.main.async {
                    self.exportProgress = Double(index + 1) / Double(history.count) * 0.8
                }
            }
            
        case .practiceResults(let score, let total, let terms):
            content += "练习结果报告\n"
            content += "练习时间: \(dateFormatter.string(from: Date()))\n"
            content += "得分: \(score)/\(total) (\(Int(Double(score)/Double(total)*100))%)\n"
            content += String(repeating: "=", count: 50) + "\n\n"
            
            content += "练习术语:\n"
            for (index, term) in terms.enumerated() {
                content += "\(index + 1). \(term.term) - \(term.meaning)\n"
            }
        }
        
        return content
    }
    
    // 生成JSON格式内容
    private func generateJSONContent(_ dataType: ExportDataType) throws -> String {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        encoder.dateEncodingStrategy = .iso8601
        
        let data: Data
        
        switch dataType {
        case .allTerms(let terms):
            let exportData = ExportedTerms(
                exportDate: Date(),
                totalCount: terms.count,
                terms: terms.map { ExportedTerm(from: $0) }
            )
            data = try encoder.encode(exportData)
            
        case .favorites(let favorites):
            let exportData = ExportedFavorites(
                exportDate: Date(),
                totalCount: favorites.count,
                favorites: favorites.map { ExportedFavorite(from: $0) }
            )
            data = try encoder.encode(exportData)
            
        case .searchHistory(let history):
            let exportData = ExportedSearchHistory(
                exportDate: Date(),
                totalCount: history.count,
                history: history.map { ExportedSearchRecord(from: $0) }
            )
            data = try encoder.encode(exportData)
            
        case .practiceResults(let score, let total, let terms):
            let exportData = ExportedPracticeResult(
                practiceDate: Date(),
                score: score,
                total: total,
                percentage: Int(Double(score)/Double(total)*100),
                terms: terms.map { ExportedTerm(from: $0) }
            )
            data = try encoder.encode(exportData)
        }
        
        guard let jsonString = String(data: data, encoding: .utf8) else {
            throw ExportError.encodingFailed
        }
        
        return jsonString
    }
    
    // 生成CSV格式内容
    private func generateCSVContent(_ dataType: ExportDataType) throws -> String {
        var content = ""
        
        switch dataType {
        case .allTerms(let terms):
            content += "术语,释义,拼音,分类,创建时间\n"
            for term in terms {
                let row = [
                    escapeCSVField(term.term),
                    escapeCSVField(term.meaning),
                    escapeCSVField(term.pinyin),
                    escapeCSVField(term.category),
                    escapeCSVField(ISO8601DateFormatter().string(from: term.createdAt))
                ].joined(separator: ",")
                content += row + "\n"
            }
            
        case .favorites(let favorites):
            content += "术语,释义,收藏时间,笔记\n"
            for favorite in favorites {
                let row = [
                    escapeCSVField(favorite.term),
                    escapeCSVField(favorite.meaning),
                    escapeCSVField(ISO8601DateFormatter().string(from: favorite.createdAt)),
                    escapeCSVField(favorite.notes)
                ].joined(separator: ",")
                content += row + "\n"
            }
            
        case .searchHistory(let history):
            content += "搜索词,搜索时间,结果数量,搜索类型\n"
            for record in history {
                let row = [
                    escapeCSVField(record.searchText),
                    escapeCSVField(ISO8601DateFormatter().string(from: record.searchedAt)),
                    String(record.resultCount),
                    escapeCSVField(record.searchType.displayName)
                ].joined(separator: ",")
                content += row + "\n"
            }
            
        case .practiceResults(let score, let total, let terms):
            content += "练习结果\n"
            content += "得分,总数,百分比,练习时间\n"
            content += "\(score),\(total),\(Int(Double(score)/Double(total)*100)),\(ISO8601DateFormatter().string(from: Date()))\n"
            content += "\n术语,释义\n"
            for term in terms {
                let row = [
                    escapeCSVField(term.term),
                    escapeCSVField(term.meaning)
                ].joined(separator: ",")
                content += row + "\n"
            }
        }
        
        return content
    }
    
    // CSV字段转义
    private func escapeCSVField(_ field: String) -> String {
        if field.contains(",") || field.contains("\"") || field.contains("\n") {
            return "\"" + field.replacingOccurrences(of: "\"", with: "\"\"") + "\""
        }
        return field
    }
    
    // 保存到文件
    private func saveToFile(content: String, format: ExportFormat, dataType: ExportDataType) throws -> URL {
        let fileName = generateFileName(format: format, dataType: dataType)
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileURL = documentsPath.appendingPathComponent(fileName)
        
        try content.write(to: fileURL, atomically: true, encoding: .utf8)
        
        DispatchQueue.main.async {
            self.exportProgress = 1.0
        }
        
        return fileURL
    }
    
    // 生成文件名
    private func generateFileName(format: ExportFormat, dataType: ExportDataType) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = dateFormatter.string(from: Date())
        
        let prefix: String
        switch dataType {
        case .allTerms: prefix = "音乐术语词典"
        case .favorites: prefix = "收藏术语"
        case .searchHistory: prefix = "搜索历史"
        case .practiceResults: prefix = "练习结果"
        }
        
        return "\(prefix)_\(timestamp).\(format.fileExtension)"
    }
    
    // 清除错误
    func clearError() {
        errorMessage = nil
    }
}

// 导出错误类型
enum ExportError: LocalizedError {
    case encodingFailed
    case fileWriteFailed
    case unsupportedFormat
    
    var errorDescription: String? {
        switch self {
        case .encodingFailed:
            return "数据编码失败"
        case .fileWriteFailed:
            return "文件写入失败"
        case .unsupportedFormat:
            return "不支持的导出格式"
        }
    }
}

// 导出数据结构
struct ExportedTerms: Codable {
    let exportDate: Date
    let totalCount: Int
    let terms: [ExportedTerm]
}

struct ExportedTerm: Codable {
    let term: String
    let meaning: String
    let pinyin: String
    let category: String
    let createdAt: Date
    
    init(from musicTerm: MusicTerm) {
        self.term = musicTerm.term
        self.meaning = musicTerm.meaning
        self.pinyin = musicTerm.pinyin
        self.category = musicTerm.category
        self.createdAt = musicTerm.createdAt
    }
}

struct ExportedFavorites: Codable {
    let exportDate: Date
    let totalCount: Int
    let favorites: [ExportedFavorite]
}

struct ExportedFavorite: Codable {
    let term: String
    let meaning: String
    let notes: String
    let createdAt: Date
    
    init(from favorite: Favorite) {
        self.term = favorite.term
        self.meaning = favorite.meaning
        self.notes = favorite.notes
        self.createdAt = favorite.createdAt
    }
}

struct ExportedSearchHistory: Codable {
    let exportDate: Date
    let totalCount: Int
    let history: [ExportedSearchRecord]
}

struct ExportedSearchRecord: Codable {
    let searchText: String
    let resultCount: Int
    let searchedAt: Date
    let searchType: String
    
    init(from searchHistory: SearchHistory) {
        self.searchText = searchHistory.searchText
        self.resultCount = searchHistory.resultCount
        self.searchedAt = searchHistory.searchedAt
        self.searchType = searchHistory.searchType.rawValue
    }
}

struct ExportedPracticeResult: Codable {
    let practiceDate: Date
    let score: Int
    let total: Int
    let percentage: Int
    let terms: [ExportedTerm]
}
