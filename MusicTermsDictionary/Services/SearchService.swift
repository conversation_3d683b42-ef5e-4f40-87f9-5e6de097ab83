import Foundation
import SwiftData

// 搜索结果结构
struct SearchResult {
    let term: MusicTerm
    let score: Double
    let matchType: MatchType
    
    enum MatchType {
        case exact          // 精确匹配
        case prefix         // 前缀匹配
        case contains       // 包含匹配
        case pinyin         // 拼音匹配
        case meaning        // 释义匹配
        case fuzzy          // 模糊匹配
    }
}

@Observable
class SearchService {
    private var modelContext: ModelContext?
    
    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext
    }
    
    // 设置模型上下文
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
    }
    
    // 主搜索方法
    func search(_ query: String, in terms: [MusicTerm]) -> [SearchResult] {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return []
        }
        
        let cleanQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        var results: [SearchResult] = []
        
        for term in terms {
            if let result = evaluateTerm(term, against: cleanQuery) {
                results.append(result)
            }
        }
        
        // 按分数和匹配类型排序
        return results.sorted { first, second in
            if first.score != second.score {
                return first.score > second.score
            }
            
            // 分数相同时，按匹配类型优先级排序
            return first.matchType.priority > second.matchType.priority
        }
    }
    
    // 评估单个术语的匹配度
    private func evaluateTerm(_ term: MusicTerm, against query: String) -> SearchResult? {
        let lowercaseQuery = query.lowercased()
        let lowercaseTerm = term.term.lowercased()
        let lowercaseMeaning = term.meaning.lowercased()
        let lowercasePinyin = term.pinyin.lowercased()
        
        var bestScore: Double = 0
        var bestMatchType: SearchResult.MatchType = .fuzzy
        
        // 1. 精确匹配 (最高优先级)
        if lowercaseTerm == lowercaseQuery {
            bestScore = 1.0
            bestMatchType = .exact
        }
        // 2. 前缀匹配
        else if lowercaseTerm.hasPrefix(lowercaseQuery) {
            bestScore = 0.9
            bestMatchType = .prefix
        }
        // 3. 包含匹配
        else if lowercaseTerm.contains(lowercaseQuery) {
            bestScore = 0.7
            bestMatchType = .contains
        }
        
        // 4. 拼音匹配
        if lowercasePinyin == lowercaseQuery {
            let pinyinScore = 0.85
            if pinyinScore > bestScore {
                bestScore = pinyinScore
                bestMatchType = .pinyin
            }
        } else if lowercasePinyin.hasPrefix(lowercaseQuery) {
            let pinyinScore = 0.75
            if pinyinScore > bestScore {
                bestScore = pinyinScore
                bestMatchType = .pinyin
            }
        } else if lowercasePinyin.contains(lowercaseQuery) {
            let pinyinScore = 0.6
            if pinyinScore > bestScore {
                bestScore = pinyinScore
                bestMatchType = .pinyin
            }
        }
        
        // 5. 释义匹配
        if lowercaseMeaning.contains(lowercaseQuery) {
            let meaningScore = 0.5
            if meaningScore > bestScore {
                bestScore = meaningScore
                bestMatchType = .meaning
            }
        }
        
        // 6. 模糊匹配 (使用编辑距离)
        let similarity = calculateSimilarity(lowercaseQuery, lowercaseTerm)
        if similarity > 0.6 {
            let fuzzyScore = similarity * 0.4
            if fuzzyScore > bestScore {
                bestScore = fuzzyScore
                bestMatchType = .fuzzy
            }
        }
        
        // 只返回分数大于阈值的结果
        if bestScore > 0.3 {
            return SearchResult(term: term, score: bestScore, matchType: bestMatchType)
        }
        
        return nil
    }
    
    // 计算字符串相似度
    private func calculateSimilarity(_ str1: String, _ str2: String) -> Double {
        let len1 = str1.count
        let len2 = str2.count
        
        if len1 == 0 { return len2 == 0 ? 1.0 : 0.0 }
        if len2 == 0 { return 0.0 }
        
        var matrix = Array(repeating: Array(repeating: 0, count: len2 + 1), count: len1 + 1)
        
        for i in 0...len1 { matrix[i][0] = i }
        for j in 0...len2 { matrix[0][j] = j }
        
        let str1Array = Array(str1)
        let str2Array = Array(str2)
        
        for i in 1...len1 {
            for j in 1...len2 {
                let cost = str1Array[i-1] == str2Array[j-1] ? 0 : 1
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      // deletion
                    matrix[i][j-1] + 1,      // insertion
                    matrix[i-1][j-1] + cost  // substitution
                )
            }
        }
        
        let maxLen = max(len1, len2)
        return 1.0 - Double(matrix[len1][len2]) / Double(maxLen)
    }
    
    // 按字母分组术语
    func groupTermsByLetter(_ terms: [MusicTerm]) -> [(String, [MusicTerm])] {
        let grouped = Dictionary(grouping: terms) { term in
            term.firstLetter
        }
        
        return grouped.sorted { $0.key < $1.key }
    }
    
    // 获取热门搜索建议
    func getSearchSuggestions(for query: String, from terms: [MusicTerm], limit: Int = 5) -> [String] {
        guard query.count >= 2 else { return [] }
        
        let lowercaseQuery = query.lowercased()
        var suggestions: Set<String> = []
        
        for term in terms {
            // 术语名称建议
            if term.term.lowercased().hasPrefix(lowercaseQuery) {
                suggestions.insert(term.term)
            }
            
            // 拼音建议
            if term.pinyin.lowercased().hasPrefix(lowercaseQuery) {
                suggestions.insert(term.term)
            }
            
            if suggestions.count >= limit { break }
        }
        
        return Array(suggestions).prefix(limit).map { String($0) }
    }
}

// 匹配类型优先级扩展
extension SearchResult.MatchType {
    var priority: Int {
        switch self {
        case .exact: return 6
        case .prefix: return 5
        case .pinyin: return 4
        case .contains: return 3
        case .meaning: return 2
        case .fuzzy: return 1
        }
    }
    
    var displayName: String {
        switch self {
        case .exact: return "精确匹配"
        case .prefix: return "前缀匹配"
        case .contains: return "包含匹配"
        case .pinyin: return "拼音匹配"
        case .meaning: return "释义匹配"
        case .fuzzy: return "模糊匹配"
        }
    }
}
