//
//  DataManager.swift
//  Music Dictionary
//
//  Created by 潘禹泽 on 8/25/25.
//

import Foundation
import SwiftData

@Observable
class DataManager {
    private var modelContext: ModelContext?
    private(set) var musicTerms: [MusicTerm] = []
    private(set) var isLoading = false
    private(set) var errorMessage: String?
    
    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext
    }
    
    // 设置模型上下文
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
        loadMusicTerms()
    }
    
    // 加载音乐术语数据
    func loadMusicTerms() {
        guard let context = modelContext else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let descriptor = FetchDescriptor<MusicTerm>(
                sortBy: [SortDescriptor(\.term)]
            )
            let existingTerms = try context.fetch(descriptor)
            
            if existingTerms.isEmpty {
                // 首次启动，从预设数据加载
                loadInitialData()
            } else {
                // 使用现有数据
                musicTerms = existingTerms
            }
        } catch {
            errorMessage = "加载数据失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // 加载初始数据
    private func loadInitialData() {
        guard let context = modelContext else { return }
        
        let initialTerms = getInitialMusicTerms()
        
        for termData in initialTerms {
            let term = MusicTerm(
                term: termData.term,
                meaning: termData.meaning,
                pinyin: termData.pinyin,
                category: termData.category
            )
            context.insert(term)
        }
        
        do {
            try context.save()
            
            // 重新获取保存后的数据
            let descriptor = FetchDescriptor<MusicTerm>(
                sortBy: [SortDescriptor(\.term)]
            )
            musicTerms = try context.fetch(descriptor)
        } catch {
            errorMessage = "保存初始数据失败: \(error.localizedDescription)"
        }
    }
    
    // 添加新术语
    func addTerm(_ term: String, meaning: String, pinyin: String = "", category: String = "") {
        guard let context = modelContext else { return }
        
        let newTerm = MusicTerm(
            term: term,
            meaning: meaning,
            pinyin: pinyin,
            category: category
        )
        
        context.insert(newTerm)
        
        do {
            try context.save()
            musicTerms.append(newTerm)
            musicTerms.sort { $0.term < $1.term }
        } catch {
            errorMessage = "添加术语失败: \(error.localizedDescription)"
        }
    }
    
    // 删除术语
    func deleteTerm(_ term: MusicTerm) {
        guard let context = modelContext else { return }
        
        context.delete(term)
        
        do {
            try context.save()
            musicTerms.removeAll { $0.id == term.id }
        } catch {
            errorMessage = "删除术语失败: \(error.localizedDescription)"
        }
    }
    
    // 更新术语
    func updateTerm(_ term: MusicTerm, newTerm: String? = nil, newMeaning: String? = nil, newPinyin: String? = nil, newCategory: String? = nil) {
        guard let context = modelContext else { return }
        
        if let newTerm = newTerm { term.term = newTerm }
        if let newMeaning = newMeaning { term.meaning = newMeaning }
        if let newPinyin = newPinyin { term.pinyin = newPinyin }
        if let newCategory = newCategory { term.category = newCategory }
        
        do {
            try context.save()
            // 重新排序
            musicTerms.sort { $0.term < $1.term }
        } catch {
            errorMessage = "更新术语失败: \(error.localizedDescription)"
        }
    }
    
    // 获取术语统计信息
    func getStatistics() -> (total: Int, categories: [String: Int], recentlyAdded: Int) {
        let total = musicTerms.count
        
        let categories = Dictionary(grouping: musicTerms, by: { $0.category })
            .mapValues { $0.count }
        
        let weekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        let recentlyAdded = musicTerms.filter { $0.createdAt >= weekAgo }.count
        
        return (total, categories, recentlyAdded)
    }
    
    // 清除错误消息
    func clearError() {
        errorMessage = nil
    }
}

// 初始音乐术语数据结构
private struct InitialTermData {
    let term: String
    let meaning: String
    let pinyin: String
    let category: String
}

// 获取初始音乐术语数据（从Python版本提取的核心术语）
private func getInitialMusicTerms() -> [InitialTermData] {
    return [
        // 基础速度术语
        InitialTermData(term: "A tempo", meaning: "速度还原", pinyin: "suduhaiyan", category: "速度术语"),
        InitialTermData(term: "Accelerando", meaning: "加速的", pinyin: "jiasu", category: "速度术语"),
        InitialTermData(term: "Adagio", meaning: "柔板；从容的；悠闲的", pinyin: "rouban", category: "速度术语"),
        InitialTermData(term: "Allegretto", meaning: "小快板；稍快；比allegro稍慢的速度", pinyin: "xiaokuaiban", category: "速度术语"),
        InitialTermData(term: "Allegro", meaning: "快板；欢快地；较活泼的速度", pinyin: "kuaiban", category: "速度术语"),
        InitialTermData(term: "Andante", meaning: "行板；徐缓；行进的", pinyin: "xingban", category: "速度术语"),
        InitialTermData(term: "Largo", meaning: "广板，很慢的", pinyin: "guangban", category: "速度术语"),
        InitialTermData(term: "Moderato", meaning: "中板，中等速度", pinyin: "zhongban", category: "速度术语"),
        InitialTermData(term: "Presto", meaning: "急板，很快的", pinyin: "jiban", category: "速度术语"),
        InitialTermData(term: "Ritardando", meaning: "渐慢", pinyin: "jianman", category: "速度术语"),
        InitialTermData(term: "Rubato", meaning: "自由速度", pinyin: "ziyou", category: "速度术语"),

        // 力度术语
        InitialTermData(term: "Crescendo", meaning: "渐强", pinyin: "jianqiang", category: "力度术语"),
        InitialTermData(term: "Diminuendo", meaning: "渐弱", pinyin: "jianruo", category: "力度术语"),
        InitialTermData(term: "Forte", meaning: "强", pinyin: "qiang", category: "力度术语"),
        InitialTermData(term: "Fortissimo", meaning: "很强", pinyin: "henqiang", category: "力度术语"),
        InitialTermData(term: "Piano", meaning: "弱", pinyin: "ruo", category: "力度术语"),
        InitialTermData(term: "Pianissimo", meaning: "很弱", pinyin: "henruo", category: "力度术语"),
        InitialTermData(term: "Mezzo forte", meaning: "中强", pinyin: "zhongqiang", category: "力度术语"),
        InitialTermData(term: "Mezzo piano", meaning: "中弱", pinyin: "zhongruo", category: "力度术语"),

        // 表情术语
        InitialTermData(term: "Affettuoso", meaning: "深情的", pinyin: "shenqing", category: "表情术语"),
        InitialTermData(term: "Appassionato", meaning: "充满激情的", pinyin: "jiqing", category: "表情术语"),
        InitialTermData(term: "Cantabile", meaning: "如歌的", pinyin: "ruge", category: "表情术语"),
        InitialTermData(term: "Con brio", meaning: "有精神，有活力的", pinyin: "huoli", category: "表情术语"),
        InitialTermData(term: "Con grazia", meaning: "优美的", pinyin: "youmei", category: "表情术语"),
        InitialTermData(term: "Dolce", meaning: "甜美的，柔和的", pinyin: "tianmei", category: "表情术语"),
        InitialTermData(term: "Espressivo", meaning: "富有表情的", pinyin: "biaoqing", category: "表情术语"),
        InitialTermData(term: "Grazioso", meaning: "优雅的", pinyin: "youya", category: "表情术语"),
        InitialTermData(term: "Maestoso", meaning: "庄严的", pinyin: "zhuangyan", category: "表情术语"),
        InitialTermData(term: "Scherzando", meaning: "诙谐的", pinyin: "huixie", category: "表情术语"),

        // 演奏技巧
        InitialTermData(term: "A cappella", meaning: "无伴奏的声乐演唱", pinyin: "wubanzou", category: "演唱技巧"),
        InitialTermData(term: "Arpeggio", meaning: "琶音", pinyin: "payin", category: "演奏技巧"),
        InitialTermData(term: "Glissando", meaning: "滑音", pinyin: "huayin", category: "演奏技巧"),
        InitialTermData(term: "Legato", meaning: "连奏，连贯的", pinyin: "lianzou", category: "演奏技巧"),
        InitialTermData(term: "Pizzicato", meaning: "拨弦", pinyin: "boxian", category: "演奏技巧"),
        InitialTermData(term: "Staccato", meaning: "断奏，跳音", pinyin: "duanzou", category: "演奏技巧"),
        InitialTermData(term: "Tenuto", meaning: "保持音的充分时值", pinyin: "baochi", category: "演奏技巧"),
        InitialTermData(term: "Tremolo", meaning: "颤音", pinyin: "chanyin", category: "演奏技巧"),
        InitialTermData(term: "Vibrato", meaning: "揉弦", pinyin: "rouxian", category: "演奏技巧"),
        InitialTermData(term: "Portamento", meaning: "滑奏", pinyin: "huazou", category: "演奏技巧"),

        // 音乐形式和结构
        InitialTermData(term: "Cadenza", meaning: "华彩段", pinyin: "huacaiduan", category: "音乐形式"),
        InitialTermData(term: "Coda", meaning: "尾声", pinyin: "weisheng", category: "音乐形式"),
        InitialTermData(term: "Da capo", meaning: "从头反复", pinyin: "congtou", category: "音乐形式"),
        InitialTermData(term: "Fine", meaning: "结束", pinyin: "jieshu", category: "音乐形式"),
        InitialTermData(term: "Ritornello", meaning: "回旋曲", pinyin: "huixuanqu", category: "音乐形式"),
        InitialTermData(term: "Segno", meaning: "记号", pinyin: "jihao", category: "音乐形式"),

        // 和声术语
        InitialTermData(term: "Arpeggiando", meaning: "琶音式的", pinyin: "payinshi", category: "和声术语"),
        InitialTermData(term: "Basso continuo", meaning: "通奏低音", pinyin: "tongzoudiyin", category: "和声术语"),
        InitialTermData(term: "Cadence", meaning: "终止式", pinyin: "zhongzhishi", category: "和声术语"),
        InitialTermData(term: "Modulation", meaning: "转调", pinyin: "zhuandiao", category: "和声术语"),

        // 装饰音
        InitialTermData(term: "Acciaccatura", meaning: "短倚音", pinyin: "duanyiyin", category: "装饰音"),
        InitialTermData(term: "Appoggiatura", meaning: "倚音", pinyin: "yiyin", category: "装饰音"),
        InitialTermData(term: "Mordent", meaning: "波音", pinyin: "boyin", category: "装饰音"),
        InitialTermData(term: "Trill", meaning: "颤音", pinyin: "chanyin", category: "装饰音"),
        InitialTermData(term: "Turn", meaning: "回音", pinyin: "huiyin", category: "装饰音")
    ]
}
