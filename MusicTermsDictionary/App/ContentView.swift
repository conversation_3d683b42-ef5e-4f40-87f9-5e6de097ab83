import SwiftUI

struct ContentView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house")
                    Text("主页")
                }
                .tag(0)
            
            FavoritesView()
                .tabItem {
                    Image(systemName: "heart")
                    Text("收藏夹")
                }
                .tag(1)
            
            HistoryView()
                .tabItem {
                    Image(systemName: "clock")
                    Text("历史记录")
                }
                .tag(2)
        }
        .accentColor(.black)
        .preferredColorScheme(.light)
    }
}

#Preview {
    ContentView()
}
