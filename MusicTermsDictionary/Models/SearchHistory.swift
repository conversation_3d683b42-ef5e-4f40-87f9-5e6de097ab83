import Foundation
import SwiftData

@Model
final class SearchHistory {
    var id: UUID
    var searchText: String
    var resultCount: Int
    var searchedAt: Date
    var searchType: SearchType
    
    init(searchText: String, resultCount: Int, searchType: SearchType = .general) {
        self.id = UUID()
        self.searchText = searchText
        self.resultCount = resultCount
        self.searchType = searchType
        self.searchedAt = Date()
    }
}

// 搜索类型枚举
enum SearchType: String, CaseIterable, Codable {
    case general = "general"        // 一般搜索
    case practice = "practice"      // 练习模式
    case favorite = "favorite"      // 收藏夹搜索
    
    var displayName: String {
        switch self {
        case .general:
            return "一般搜索"
        case .practice:
            return "练习模式"
        case .favorite:
            return "收藏夹搜索"
        }
    }
    
    var icon: String {
        switch self {
        case .general:
            return "magnifyingglass"
        case .practice:
            return "brain.head.profile"
        case .favorite:
            return "heart"
        }
    }
}

// 扩展：便利方法和计算属性
extension SearchHistory {
    // 格式化搜索时间
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: searchedAt)
    }
    
    // 相对时间显示
    var relativeTime: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: searchedAt, relativeTo: Date())
    }
    
    // 搜索结果描述
    var resultDescription: String {
        if resultCount == 0 {
            return "无结果"
        } else if resultCount == 1 {
            return "1个结果"
        } else {
            return "\(resultCount)个结果"
        }
    }
    
    // 检查是否为今天的搜索
    var isToday: Bool {
        Calendar.current.isDateInToday(searchedAt)
    }
    
    // 检查是否为本周的搜索
    var isThisWeek: Bool {
        let weekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        return searchedAt >= weekAgo
    }
}

// 扩展：搜索历史分组
extension Array where Element == SearchHistory {
    // 按日期分组
    func groupedByDate() -> [(String, [SearchHistory])] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: self) { history in
            if calendar.isDateInToday(history.searchedAt) {
                return "今天"
            } else if calendar.isDateInYesterday(history.searchedAt) {
                return "昨天"
            } else if history.isThisWeek {
                let formatter = DateFormatter()
                formatter.dateFormat = "EEEE"
                return formatter.string(from: history.searchedAt)
            } else {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return formatter.string(from: history.searchedAt)
            }
        }
        
        // 按时间排序
        return grouped.sorted { first, second in
            guard let firstDate = first.value.first?.searchedAt,
                  let secondDate = second.value.first?.searchedAt else {
                return false
            }
            return firstDate > secondDate
        }
    }
    
    // 获取最近的搜索记录
    func recent(limit: Int = 10) -> [SearchHistory] {
        return self.sorted { $0.searchedAt > $1.searchedAt }.prefix(limit).map { $0 }
    }
    
    // 获取热门搜索词
    func popularSearches(limit: Int = 5) -> [(String, Int)] {
        let searchCounts = Dictionary(grouping: self, by: { $0.searchText.lowercased() })
            .mapValues { $0.count }
        
        return searchCounts.sorted { $0.value > $1.value }
            .prefix(limit)
            .map { ($0.key, $0.value) }
    }
}
