import Foundation
import SwiftData

@Model
final class Favorite {
    var id: UUID
    var termId: UUID
    var term: String
    var meaning: String
    var createdAt: Date
    var notes: String // 用户自定义笔记
    
    init(termId: UUID, term: String, meaning: String, notes: String = "") {
        self.id = UUID()
        self.termId = termId
        self.term = term
        self.meaning = meaning
        self.notes = notes
        self.createdAt = Date()
    }
    
    // 更新笔记
    func updateNotes(_ newNotes: String) {
        self.notes = newNotes
    }
}

// 扩展：便利方法
extension Favorite {
    // 从MusicTerm创建收藏
    static func from(musicTerm: MusicTerm, notes: String = "") -> Favorite {
        return Favorite(
            termId: musicTerm.id,
            term: musicTerm.term,
            meaning: musicTerm.meaning,
            notes: notes
        )
    }
    
    // 检查是否匹配搜索文本
    func matches(searchText: String) -> Bool {
        let lowercaseSearch = searchText.lowercased()
        
        return term.lowercased().contains(lowercaseSearch) ||
               meaning.lowercased().contains(lowercaseSearch) ||
               notes.lowercased().contains(lowercaseSearch)
    }
}
