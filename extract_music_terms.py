#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐术语提取脚本
从Python项目中提取所有音乐术语，转换为iOS项目需要的Swift格式
包含自动拼音生成功能
"""

import re
import ast
from pypinyin import lazy_pinyin, Style


def extract_music_terms_from_file(file_path):
    """从Python文件中提取音乐术语字典"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找music_terms字典的定义
    pattern = r'music_terms\s*=\s*\{(.*?)\}'
    match = re.search(pattern, content, re.DOTALL)
    
    if not match:
        print("未找到music_terms字典定义")
        return {}
    
    dict_content = match.group(1)
    
    # 解析字典内容
    terms = {}
    lines = dict_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#'):
            continue
            
        # 匹配键值对
        match = re.match(r"'([^']+)':\s*'([^']+)',?", line)
        if match:
            key = match.group(1)
            value = match.group(2)
            terms[key] = value
    
    # 查找music_terms.update()调用
    update_pattern = r'music_terms\.update\(\{(.*?)\}\)'
    update_matches = re.findall(update_pattern, content, re.DOTALL)
    
    for update_content in update_matches:
        lines = update_content.split('\n')
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            match = re.match(r"'([^']+)':\s*'([^']+)',?", line)
            if match:
                key = match.group(1)
                value = match.group(2)
                terms[key] = value
    
    return terms

def generate_pinyin(text):
    """生成中文文本的拼音"""
    # 如果是英文术语，返回空字符串
    if all(ord(char) < 128 for char in text):
        return ""
    
    # 生成拼音
    pinyin_list = lazy_pinyin(text, style=Style.NORMAL)
    return ''.join(pinyin_list).lower()

def categorize_term(term, meaning):
    """根据术语内容自动分类"""
    term_lower = term.lower()
    meaning_lower = meaning.lower()
    
    # 速度术语
    speed_keywords = ['快', '慢', '速度', 'allegro', 'andante', 'adagio', 'presto', 'largo', 'moderato', 'tempo', 'accelerando', 'ritardando', 'rallentando']
    if any(keyword in term_lower or keyword in meaning_lower for keyword in speed_keywords):
        return "速度术语"
    
    # 力度术语
    dynamics_keywords = ['强', '弱', 'forte', 'piano', 'crescendo', 'diminuendo', 'sforzando', 'fortissimo', 'pianissimo']
    if any(keyword in term_lower or keyword in meaning_lower for keyword in dynamics_keywords):
        return "力度术语"
    
    # 表情术语
    expression_keywords = ['甜美', '柔和', '深情', '激情', '优雅', '庄严', '活泼', '神秘', 'dolce', 'espressivo', 'cantabile', 'grazioso', 'maestoso', 'vivace']
    if any(keyword in term_lower or keyword in meaning_lower for keyword in expression_keywords):
        return "表情术语"
    
    # 演奏技巧
    technique_keywords = ['奏', '弦', '音', '技巧', 'staccato', 'legato', 'pizzicato', 'vibrato', 'tremolo', 'glissando', 'arpeggio', '拨', '按', '滑', '颤']
    if any(keyword in term_lower or keyword in meaning_lower for keyword in technique_keywords):
        return "演奏技巧"
    
    # 音乐形式
    form_keywords = ['曲', '段', '部', '式', 'coda', 'cadenza', 'rondo', 'sonata', 'suite', 'variation', '华彩', '尾声', '回旋']
    if any(keyword in term_lower or keyword in meaning_lower for keyword in form_keywords):
        return "音乐形式"
    
    # 和声术语
    harmony_keywords = ['和弦', '和声', '调', '音程', 'chord', 'harmony', 'cadence', 'modulation', '终止', '转调']
    if any(keyword in term_lower or keyword in meaning_lower for keyword in harmony_keywords):
        return "和声术语"
    
    # 装饰音
    ornament_keywords = ['装饰', '倚音', '波音', '颤音', '回音', 'trill', 'mordent', 'turn', 'appoggiatura', 'acciaccatura']
    if any(keyword in term_lower or keyword in meaning_lower for keyword in ornament_keywords):
        return "装饰音"
    
    # 默认分类
    return "其他术语"

def generate_swift_code(terms_dict):
    """生成Swift代码格式的术语数据"""
    swift_code = """//
//  Generated Music Terms Data
//  从Python项目自动提取生成
//

private func getInitialMusicTerms() -> [InitialTermData] {
    return [
"""
    
    # 按分类组织术语
    categories = {}
    for term, meaning in terms_dict.items():
        category = categorize_term(term, meaning)
        if category not in categories:
            categories[category] = []
        categories[category].append((term, meaning))
    
    # 生成Swift代码
    for category, terms_list in sorted(categories.items()):
        swift_code += f"\n        // {category}\n"
        
        for term, meaning in sorted(terms_list):
            # 清理术语和释义中的特殊字符
            clean_term = term.replace('"', '\\"').replace("'", "\\'")
            clean_meaning = meaning.replace('"', '\\"').replace("'", "\\'")
            
            # 生成拼音
            pinyin = generate_pinyin(clean_meaning)
            
            swift_code += f'        InitialTermData(term: "{clean_term}", meaning: "{clean_meaning}", pinyin: "{pinyin}", category: "{category}"),\n'
    
    swift_code += """    ]
}"""
    
    return swift_code

def main():
    """主函数"""
    print("🎵 开始提取音乐术语...")
    
    # 提取术语
    python_file = "music_terms_dictionary-main/music_dict on computer.py"
    terms = extract_music_terms_from_file(python_file)
    
    print(f"✅ 成功提取 {len(terms)} 个音乐术语")
    
    # 生成Swift代码
    swift_code = generate_swift_code(terms)
    
    # 保存到文件
    output_file = "/Users/<USER>/Desktop/开发/iOS音乐辞典/Music Dictionary/generated_music_terms.swift"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(swift_code)
    
    print(f"✅ Swift代码已生成到: {output_file}")
    
    # 统计信息
    categories = {}
    for term, meaning in terms.items():
        category = categorize_term(term, meaning)
        categories[category] = categories.get(category, 0) + 1
    
    print("\n📊 术语分类统计:")
    for category, count in sorted(categories.items()):
        print(f"  {category}: {count}个")
    
    print(f"\n🎉 总计: {len(terms)}个音乐术语")
    print("\n💡 接下来请:")
    print("1. 将生成的代码复制到 DataManager.swift 中的 getInitialMusicTerms() 函数")
    print("2. 删除现有的初始数据")
    print("3. 重新运行iOS应用以加载新数据")

if __name__ == "__main__":
    main()
