// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A000001 /* MusicTermsDictionaryApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000000 /* MusicTermsDictionaryApp.swift */; };
		1A000003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000002 /* ContentView.swift */; };
		1A000005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A000004 /* Assets.xcassets */; };
		1A000008 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A000007 /* Preview Assets.xcassets */; };
		1A000010 /* MusicTerm.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00000F /* MusicTerm.swift */; };
		1A000012 /* Favorite.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000011 /* Favorite.swift */; };
		1A000014 /* SearchHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000013 /* SearchHistory.swift */; };
		1A000016 /* SearchService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000015 /* SearchService.swift */; };
		1A000018 /* DataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000017 /* DataManager.swift */; };
		1A00001A /* ExportService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000019 /* ExportService.swift */; };
		1A00001C /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00001B /* HomeView.swift */; };
		1A00001E /* SearchBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00001D /* SearchBar.swift */; };
		1A000020 /* TermCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00001F /* TermCard.swift */; };
		1A000022 /* FavoritesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000021 /* FavoritesView.swift */; };
		1A000024 /* HistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000023 /* HistoryView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A000000 /* MusicTermsDictionaryApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicTermsDictionaryApp.swift; sourceTree = "<group>"; };
		1A000002 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1A000004 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A000007 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		1A00000F /* MusicTerm.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicTerm.swift; sourceTree = "<group>"; };
		1A000011 /* Favorite.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Favorite.swift; sourceTree = "<group>"; };
		1A000013 /* SearchHistory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchHistory.swift; sourceTree = "<group>"; };
		1A000015 /* SearchService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchService.swift; sourceTree = "<group>"; };
		1A000017 /* DataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataManager.swift; sourceTree = "<group>"; };
		1A000019 /* ExportService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExportService.swift; sourceTree = "<group>"; };
		1A00001B /* HomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		1A00001D /* SearchBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchBar.swift; sourceTree = "<group>"; };
		1A00001F /* TermCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TermCard.swift; sourceTree = "<group>"; };
		1A000021 /* FavoritesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoritesView.swift; sourceTree = "<group>"; };
		1A000023 /* HistoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A000025 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A000026 = {
			isa = PBXGroup;
			children = (
				1A000027 /* MusicTermsDictionary */,
			);
			sourceTree = "<group>";
		};
		1A000027 /* MusicTermsDictionary */ = {
			isa = PBXGroup;
			children = (
				1A000028 /* App */,
				1A000029 /* Models */,
				1A00002A /* Views */,
				1A00002B /* Services */,
				1A000004 /* Assets.xcassets */,
				1A000006 /* Preview Content */,
			);
			path = MusicTermsDictionary;
			sourceTree = "<group>";
		};
		1A000028 /* App */ = {
			isa = PBXGroup;
			children = (
				1A000000 /* MusicTermsDictionaryApp.swift */,
				1A000002 /* ContentView.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		1A000029 /* Models */ = {
			isa = PBXGroup;
			children = (
				1A00000F /* MusicTerm.swift */,
				1A000011 /* Favorite.swift */,
				1A000013 /* SearchHistory.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		1A00002A /* Views */ = {
			isa = PBXGroup;
			children = (
				1A00002C /* Home */,
				1A00002D /* Favorites */,
				1A00002E /* History */,
				1A00002F /* Components */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1A00002B /* Services */ = {
			isa = PBXGroup;
			children = (
				1A000015 /* SearchService.swift */,
				1A000017 /* DataManager.swift */,
				1A000019 /* ExportService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		1A00002C /* Home */ = {
			isa = PBXGroup;
			children = (
				1A00001B /* HomeView.swift */,
				1A00001D /* SearchBar.swift */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		1A00002D /* Favorites */ = {
			isa = PBXGroup;
			children = (
				1A000021 /* FavoritesView.swift */,
			);
			path = Favorites;
			sourceTree = "<group>";
		};
		1A00002E /* History */ = {
			isa = PBXGroup;
			children = (
				1A000023 /* HistoryView.swift */,
			);
			path = History;
			sourceTree = "<group>";
		};
		1A00002F /* Components */ = {
			isa = PBXGroup;
			children = (
				1A00001F /* TermCard.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		1A000006 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1A000007 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A000030 /* MusicTermsDictionary */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A000031 /* Build configuration list for PBXNativeTarget "MusicTermsDictionary" */;
			buildPhases = (
				1A000032 /* Sources */,
				1A000025 /* Frameworks */,
				1A000033 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MusicTermsDictionary;
			productName = MusicTermsDictionary;
			productReference = 1A000034 /* MusicTermsDictionary.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A000035 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					1A000030 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A000036 /* Build configuration list for PBXProject "MusicTermsDictionary" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A000026;
			productRefGroup = 1A000037 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A000030 /* MusicTermsDictionary */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A000033 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A000008 /* Preview Assets.xcassets in Resources */,
				1A000005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A000032 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A000003 /* ContentView.swift in Sources */,
				1A000010 /* MusicTerm.swift in Sources */,
				1A000012 /* Favorite.swift in Sources */,
				1A000014 /* SearchHistory.swift in Sources */,
				1A000016 /* SearchService.swift in Sources */,
				1A000018 /* DataManager.swift in Sources */,
				1A00001A /* ExportService.swift in Sources */,
				1A00001C /* HomeView.swift in Sources */,
				1A00001E /* SearchBar.swift in Sources */,
				1A000020 /* TermCard.swift in Sources */,
				1A000022 /* FavoritesView.swift in Sources */,
				1A000024 /* HistoryView.swift in Sources */,
				1A000001 /* MusicTermsDictionaryApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A000038 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A000039 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1A00003A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MusicTermsDictionary/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yuze.MusicTermsDictionary;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1A00003B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MusicTermsDictionary/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yuze.MusicTermsDictionary;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A000031 /* Build configuration list for PBXNativeTarget "MusicTermsDictionary" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A00003A /* Debug */,
				1A00003B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A000036 /* Build configuration list for PBXProject "MusicTermsDictionary" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A000038 /* Debug */,
				1A000039 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A000035 /* Project object */;
}
